{% extends "layout.html" %}

{% block title %}Reset Password - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="auth-card">
            <div class="auth-header">
                <h2><i class="bi bi-shield-lock"></i> Set New Password</h2>
                <p class="text-muted">Enter your new password below</p>
            </div>

            <form method="POST" class="auth-form" action="{{ url_for('auth.reset_password', token=request.view_args.token) }}" id="reset-password-form">
                {{ form.hidden_tag() }}

                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""),
                        placeholder="Enter new password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="bi bi-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="password-requirements">
                        <small class="text-muted">
                            Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.
                        </small>
                    </div>
                </div>

                <div class="form-group">
                    {{ form.password_confirm.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors else ""),
                        placeholder="Confirm new password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('password_confirm')">
                            <i class="bi bi-eye" id="password_confirm-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password_confirm.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password_confirm.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-primary btn-auth">
                    <i class="bi bi-check-circle"></i> Reset Password
                </button>
            </form>

            <div class="auth-links">
                <hr>
                <p class="text-center">
                    <a href="{{ url_for('auth.login') }}" class="auth-link">
                        <i class="bi bi-arrow-left"></i> Back to Login
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
{% endblock %}