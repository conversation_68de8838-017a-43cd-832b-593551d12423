{% extends "layout.html" %}

{% block title %}Forgot Password - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="auth-card">
            <div class="auth-header">
                <h2><i class="bi bi-key"></i> Reset Password</h2>
                <p class="text-muted">Enter your email address and we'll send you a link to reset your password</p>
            </div>

            <form method="POST" class="auth-form" action="{{ url_for('auth.forgot_password') }}" id="forgot-password-form">
                {{ form.hidden_tag() }}

                <div class="form-group">
                    {{ form.email.label(class="form-label") }}
                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""),
                    placeholder="Enter your email address") }}
                    {% if form.email.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.email.errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <button type="submit" class="btn btn-primary btn-auth">
                    <i class="bi bi-envelope"></i> Send Reset Link
                </button>
            </form>

            <div class="auth-links">
                <hr>
                <p class="text-center">
                    <a href="{{ url_for('auth.login') }}" class="auth-link">
                        <i class="bi bi-arrow-left"></i> Back to Login
                    </a>
                </p>
                <p class="text-center">
                    Don't have an account?
                    <a href="{{ url_for('auth.register') }}" class="auth-link-primary">Sign up here</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
{% endblock %}