"""
Authentication routes for the NewsMonitor web application.

This module provides routes for user registration, login, logout, and profile management.
"""

from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from urllib.parse import urlparse

from web.auth.forms import (
    LoginForm, RegistrationForm, ProfileForm, ChangePasswordForm,
    ForgotPasswordForm, ResetPasswordForm, EmailPreferencesForm
)
from web.auth.utils import (
    get_user_by_username_or_email, create_user, update_user_login,
    update_user_profile, change_user_password, verify_user_email,
    generate_confirmation_token, confirm_token, generate_reset_token,
    confirm_reset_token, get_redirect_target, rate_limit_check,
    log_security_event, validate_password_strength
)
from utils.logging_config import get_web_logger
from web.models import User
from web.database import db

# Create authentication blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

# Configure logging
logger = get_web_logger(__name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login route."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = LoginForm()

    if form.validate_on_submit():
        # Rate limiting check
        if not rate_limit_check(f"login_{request.remote_addr}", limit=5, window=300):
            flash('Too many login attempts. Please try again later.', 'error')
            return render_template('auth/login.html', form=form)

        # Get user by username or email
        user = get_user_by_username_or_email(form.username.data)

        if user and user.check_password(form.password.data) and user.is_active:
            # Update last login
            update_user_login(user)

            # Log user in
            login_user(user, remember=form.remember_me.data)

            # Log security event
            log_security_event(user.id, 'login_success',
                               ip_address=request.remote_addr)

            flash(
                f'Welcome back, {user.first_name or user.username}!', 'success')

            # Redirect to next page or home
            next_page = request.args.get('next')
            if not next_page or urlparse(next_page).netloc != '':
                next_page = url_for('index')
            return redirect(next_page)
        else:
            # Log failed login attempt
            if user:
                log_security_event(user.id, 'login_failed',
                                   'Invalid password', request.remote_addr)
            else:
                log_security_event(
                    None, 'login_failed', f'Unknown user: {form.username.data}', request.remote_addr)

            flash('Invalid username/email or password.', 'error')

    return render_template('auth/login.html', form=form)


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """User registration route."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = RegistrationForm()

    if form.validate_on_submit():
        # Rate limiting check
        if not rate_limit_check(f"register_{request.remote_addr}", limit=3, window=600):
            flash('Too many registration attempts. Please try again later.', 'error')
            return render_template('auth/register.html', form=form)

        # Validate password strength
        password_errors = validate_password_strength(form.password.data)
        if password_errors:
            for error in password_errors:
                flash(error, 'error')
            return render_template('auth/register.html', form=form)

        # Create new user
        user = create_user(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data
        )

        if user:
            # Log security event
            log_security_event(user.id, 'user_registered',
                               ip_address=request.remote_addr)

            flash(
                'Registration successful! Please check your email to verify your account.', 'success')

            # TODO: Send verification email
            # send_verification_email(user)

            return redirect(url_for('auth.login'))
        else:
            flash('Registration failed. Please try again.', 'error')

    return render_template('auth/register.html', form=form)


@auth_bp.route('/logout')
@login_required
def logout():
    """User logout route."""
    user_id = current_user.id
    logout_user()

    # Log security event
    log_security_event(user_id, 'logout', ip_address=request.remote_addr)

    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('index'))


@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """User profile management route."""
    form = ProfileForm()

    if form.validate_on_submit():
        # Update user profile
        email_preferences = {
            'daily_summary': form.daily_summary.data,
            'market_alerts': form.market_alerts.data,
            'news_digest': form.news_digest.data,
            'frequency': form.email_frequency.data
        }

        user_preferences = {
            'theme': form.theme.data,
            'timezone': form.timezone.data
        }

        success = update_user_profile(
            current_user,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            email=form.email.data,
            email_preferences=email_preferences,
            user_preferences=user_preferences
        )

        if success:
            flash('Profile updated successfully!', 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash('Error updating profile. Please try again.', 'error')

    # Pre-populate form with current user data
    if request.method == 'GET':
        form.first_name.data = current_user.first_name
        form.last_name.data = current_user.last_name
        form.email.data = current_user.email

        # Email preferences
        if current_user.email_preferences:
            form.daily_summary.data = current_user.email_preferences.get(
                'daily_summary', True)
            form.market_alerts.data = current_user.email_preferences.get(
                'market_alerts', True)
            form.news_digest.data = current_user.email_preferences.get(
                'news_digest', True)
            form.email_frequency.data = current_user.email_preferences.get(
                'frequency', 'daily')

        # User preferences
        if current_user.user_preferences:
            form.theme.data = current_user.user_preferences.get(
                'theme', 'light')
            form.timezone.data = current_user.user_preferences.get(
                'timezone', 'UTC')

    return render_template('auth/profile.html', form=form)


@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change password route."""
    form = ChangePasswordForm()

    if form.validate_on_submit():
        # Verify current password
        if not current_user.check_password(form.current_password.data):
            flash('Current password is incorrect.', 'error')
            return render_template('auth/change_password.html', form=form)

        # Validate new password strength
        password_errors = validate_password_strength(form.new_password.data)
        if password_errors:
            for error in password_errors:
                flash(error, 'error')
            return render_template('auth/change_password.html', form=form)

        # Change password
        success = change_user_password(current_user, form.new_password.data)

        if success:
            # Log security event
            log_security_event(
                current_user.id, 'password_changed', ip_address=request.remote_addr)

            flash('Password changed successfully!', 'success')
            return redirect(url_for('auth.profile'))
        else:
            flash('Error changing password. Please try again.', 'error')

    return render_template('auth/change_password.html', form=form)


@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """Forgot password route."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    form = ForgotPasswordForm()

    if form.validate_on_submit():
        # Rate limiting check
        if not rate_limit_check(f"forgot_password_{request.remote_addr}", limit=1000, window=600):
            flash('Too many password reset attempts. Please try again later.', 'error')
            return render_template('auth/forgot_password.html', form=form)
        user = get_user_by_username_or_email(form.email.data)
        if user and user.is_active:
            # Generate reset token
            logger.info(f"User {user.username} requested password reset")
            token = generate_reset_token(user.id)

            # Send password reset email
            from web import email_service
            email_sent = email_service.send_password_reset_email(user, token)
            
            if email_sent:
                logger.info(f"Password reset email sent to {user.email}")
            else:
                logger.error(f"Failed to send password reset email to {user.email}")

            # Log security event
            log_security_event(
                user.id, 'password_reset_requested', ip_address=request.remote_addr)

        # Always show success message to prevent email enumeration
        flash('If an account with that email exists, you will receive password reset instructions.', 'info')
        return redirect(url_for('auth.login'))

    return render_template('auth/forgot_password.html', form=form)


@auth_bp.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    """Reset password route."""
    if current_user.is_authenticated:
        return redirect(url_for('index'))

    # Verify token
    user_id = confirm_reset_token(token)
    if not user_id:
        flash('Invalid or expired reset token.', 'error')
        return redirect(url_for('auth.forgot_password'))

    # Get user
    try:
        user = db.session.get(User, user_id)
        if not user or not user.is_active:
            flash('Invalid reset token.', 'error')
            return redirect(url_for('auth.forgot_password'))
    except Exception as e:
        current_app.logger.error(f"Error getting user for password reset: {e}")
        flash('Error processing reset request.', 'error')
        return redirect(url_for('auth.forgot_password'))

    form = ResetPasswordForm()

    if form.validate_on_submit():
        # Validate password strength
        password_errors = validate_password_strength(form.password.data)
        if password_errors:
            for error in password_errors:
                flash(error, 'error')
            return render_template('auth/reset_password.html', form=form)

        # Change password
        success = change_user_password(user, form.password.data)

        if success:
            # Log security event
            log_security_event(
                user.id, 'password_reset_completed', ip_address=request.remote_addr)

            flash(
                'Password reset successfully! You can now log in with your new password.', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('Error resetting password. Please try again.', 'error')

    return render_template('auth/reset_password.html', form=form)


@auth_bp.route('/verify-email/<token>')
def verify_email(token):
    """Email verification route."""
    if current_user.is_authenticated and current_user.is_verified:
        flash('Your email is already verified.', 'info')
        return redirect(url_for('index'))

    # Verify token
    email = confirm_token(token)
    if not email:
        flash('Invalid or expired verification token.', 'error')
        return redirect(url_for('index'))

    # Get user by email
    user = get_user_by_username_or_email(email)
    if not user:
        flash('Invalid verification token.', 'error')
        return redirect(url_for('index'))

    if user.is_verified:
        flash('Your email is already verified.', 'info')
    else:
        # Verify user email
        success = verify_user_email(user)
        if success:
            # Log security event
            log_security_event(user.id, 'email_verified',
                               ip_address=request.remote_addr)

            flash('Email verified successfully!', 'success')
        else:
            flash('Error verifying email. Please try again.', 'error')

    return redirect(url_for('index'))


@auth_bp.route('/unverified')
@login_required
def unverified():
    """Unverified email page."""
    if current_user.is_verified:
        return redirect(url_for('index'))

    return render_template('auth/unverified.html')


@auth_bp.route('/api/user-info')
@login_required
def api_user_info():
    """API endpoint to get current user information."""
    return jsonify(current_user.to_dict())


@auth_bp.route('/api/email-preferences', methods=['GET', 'POST'])
@login_required
def api_email_preferences():
    """API endpoint to manage email preferences."""
    if request.method == 'GET':
        return jsonify(current_user.email_preferences or {})

    elif request.method == 'POST':
        try:
            preferences = request.get_json()

            # Validate preferences
            valid_keys = ['daily_summary', 'market_alerts',
                          'news_digest', 'frequency']
            filtered_preferences = {
                k: v for k, v in preferences.items() if k in valid_keys}

            # Update user preferences
            success = update_user_profile(
                current_user, email_preferences=filtered_preferences)

            if success:
                return jsonify({'success': True, 'preferences': filtered_preferences})
            else:
                return jsonify({'success': False, 'error': 'Failed to update preferences'}), 500

        except Exception as e:
            current_app.logger.error(f"Error updating email preferences: {e}")
            return jsonify({'success': False, 'error': 'Invalid request'}), 400
