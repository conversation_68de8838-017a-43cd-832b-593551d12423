/**
 * Market Utilities Module
 * Shared utilities for real-time market data updates and market status detection
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// Market Configuration Constants
const MARKET_CONFIG = {
    HOURS: {
        OPEN: { hour: 9, minute: 30 },   // 9:30 AM ET
        CLOSE: { hour: 16, minute: 0 }   // 4:00 PM ET
    },
    
    UPDATE_INTERVALS: {
        MARKET_OPEN: 2 * 60 * 1000,      // 2 minutes during market hours
        MARKET_CLOSED: 30 * 60 * 1000,   // 30 minutes when market is closed
        STATUS_CHECK: 60 * 1000          // 1 minute for status checks
    },
    
    TIMEZONE: 'America/New_York'
};

/**
 * Market Status Service
 * Provides market status detection and real-time update management
 */
class MarketStatusService {
    constructor() {
        this.lastMarketStatus = null;
        this.statusCheckInterval = null;
        this.subscribers = new Map(); // Map of subscriber ID to callback function
        this.subscriberCounter = 0;
        
        this.init();
    }

    init() {
        this.startStatusMonitoring();
    }

    /**
     * Get current market status (open/closed)
     * @returns {object} Market status information
     */
    getMarketStatus() {
        const now = new Date();
        const et = new Date(now.toLocaleString("en-US", {timeZone: MARKET_CONFIG.TIMEZONE}));
        const day = et.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
        const hour = et.getHours();
        const minute = et.getMinutes();
        
        // Check if it's a weekday (Monday-Friday)
        const isWeekday = day >= 1 && day <= 5;
        
        // Check if it's within market hours (9:30 AM - 4:00 PM ET)
        const currentMinutes = hour * 60 + minute;
        const openMinutes = MARKET_CONFIG.HOURS.OPEN.hour * 60 + MARKET_CONFIG.HOURS.OPEN.minute;
        const closeMinutes = MARKET_CONFIG.HOURS.CLOSE.hour * 60 + MARKET_CONFIG.HOURS.CLOSE.minute;
        const isWithinHours = currentMinutes >= openMinutes && currentMinutes < closeMinutes;
        
        const isOpen = isWeekday && isWithinHours;
        
        let status;
        if (!isWeekday) {
            status = 'Weekend';
        } else if (currentMinutes < openMinutes) {
            status = 'Pre-market';
        } else if (currentMinutes >= closeMinutes) {
            status = 'After-hours';
        } else {
            status = 'Open';
        }
        
        return {
            isOpen,
            status,
            currentTime: et.toLocaleTimeString('en-US', { 
                timeZone: MARKET_CONFIG.TIMEZONE,
                hour12: true 
            }),
            nextOpen: this.getNextMarketOpen(et),
            nextClose: this.getNextMarketClose(et)
        };
    }

    /**
     * Get the next market open time
     * @param {Date} currentTime - Current ET time
     * @returns {Date} Next market open time
     */
    getNextMarketOpen(currentTime) {
        const nextOpen = new Date(currentTime);
        const currentDay = nextOpen.getDay();
        const currentHour = nextOpen.getHours();
        const currentMinute = nextOpen.getMinutes();
        
        // If it's a weekday and before market open, next open is today
        if (currentDay >= 1 && currentDay <= 5) {
            const currentMinutes = currentHour * 60 + currentMinute;
            const openMinutes = MARKET_CONFIG.HOURS.OPEN.hour * 60 + MARKET_CONFIG.HOURS.OPEN.minute;
            
            if (currentMinutes < openMinutes) {
                nextOpen.setHours(MARKET_CONFIG.HOURS.OPEN.hour, MARKET_CONFIG.HOURS.OPEN.minute, 0, 0);
                return nextOpen;
            }
        }
        
        // Otherwise, find next Monday
        const daysUntilMonday = currentDay === 0 ? 1 : (8 - currentDay);
        nextOpen.setDate(nextOpen.getDate() + daysUntilMonday);
        nextOpen.setHours(MARKET_CONFIG.HOURS.OPEN.hour, MARKET_CONFIG.HOURS.OPEN.minute, 0, 0);
        
        return nextOpen;
    }

    /**
     * Get the next market close time
     * @param {Date} currentTime - Current ET time
     * @returns {Date} Next market close time
     */
    getNextMarketClose(currentTime) {
        const nextClose = new Date(currentTime);
        const currentDay = nextClose.getDay();
        
        // If it's a weekday and market is open, next close is today
        if (currentDay >= 1 && currentDay <= 5) {
            const marketStatus = this.getMarketStatus();
            if (marketStatus.isOpen) {
                nextClose.setHours(MARKET_CONFIG.HOURS.CLOSE.hour, MARKET_CONFIG.HOURS.CLOSE.minute, 0, 0);
                return nextClose;
            }
        }
        
        // Otherwise, find next Friday
        const daysUntilFriday = currentDay <= 5 ? (5 - currentDay) : (7 - currentDay + 5);
        nextClose.setDate(nextClose.getDate() + daysUntilFriday);
        nextClose.setHours(MARKET_CONFIG.HOURS.CLOSE.hour, MARKET_CONFIG.HOURS.CLOSE.minute, 0, 0);
        
        return nextClose;
    }

    /**
     * Subscribe to market status changes
     * @param {Function} callback - Function to call when market status changes
     * @returns {number} Subscriber ID for unsubscribing
     */
    subscribe(callback) {
        const subscriberId = ++this.subscriberCounter;
        this.subscribers.set(subscriberId, callback);
        
        // Immediately call with current status
        const currentStatus = this.getMarketStatus();
        callback(currentStatus);
        
        return subscriberId;
    }

    /**
     * Unsubscribe from market status changes
     * @param {number} subscriberId - Subscriber ID returned from subscribe()
     */
    unsubscribe(subscriberId) {
        this.subscribers.delete(subscriberId);
    }

    /**
     * Start monitoring market status changes
     */
    startStatusMonitoring() {
        const checkStatus = () => {
            const currentStatus = this.getMarketStatus();
            
            // Check if status changed
            if (this.lastMarketStatus === null || 
                this.lastMarketStatus.isOpen !== currentStatus.isOpen ||
                this.lastMarketStatus.status !== currentStatus.status) {
                
                console.log(`Market status changed: ${currentStatus.status} (${currentStatus.isOpen ? 'Open' : 'Closed'})`);
                
                // Notify all subscribers
                this.subscribers.forEach(callback => {
                    try {
                        callback(currentStatus);
                    } catch (error) {
                        console.error('Error in market status subscriber callback:', error);
                    }
                });
                
                this.lastMarketStatus = currentStatus;
            }
        };
        
        // Initial check
        checkStatus();
        
        // Check status every minute
        this.statusCheckInterval = setInterval(checkStatus, MARKET_CONFIG.UPDATE_INTERVALS.STATUS_CHECK);
    }

    /**
     * Stop monitoring market status changes
     */
    stopStatusMonitoring() {
        if (this.statusCheckInterval) {
            clearInterval(this.statusCheckInterval);
            this.statusCheckInterval = null;
        }
    }

    /**
     * Get recommended update interval based on market status
     * @returns {number} Update interval in milliseconds
     */
    getUpdateInterval() {
        const marketStatus = this.getMarketStatus();
        return marketStatus.isOpen ? 
            MARKET_CONFIG.UPDATE_INTERVALS.MARKET_OPEN : 
            MARKET_CONFIG.UPDATE_INTERVALS.MARKET_CLOSED;
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this.stopStatusMonitoring();
        this.subscribers.clear();
    }
}

/**
 * Real-time Update Manager
 * Manages real-time updates for multiple components
 */
class RealTimeUpdateManager {
    constructor(marketStatusService) {
        this.marketStatusService = marketStatusService;
        this.updateIntervals = new Map(); // Map of component ID to interval
        this.updateCallbacks = new Map(); // Map of component ID to callback function
        this.componentCounter = 0;
        
        // Subscribe to market status changes
        this.statusSubscriptionId = this.marketStatusService.subscribe(
            this.onMarketStatusChange.bind(this)
        );
    }

    /**
     * Register a component for real-time updates
     * @param {Function} updateCallback - Function to call for updates
     * @param {string} componentName - Optional name for logging
     * @returns {number} Component ID for unregistering
     */
    register(updateCallback, componentName = 'Unknown') {
        const componentId = ++this.componentCounter;
        this.updateCallbacks.set(componentId, {
            callback: updateCallback,
            name: componentName
        });
        
        // Start updates with current interval
        this.startUpdates(componentId);
        
        console.log(`Registered component "${componentName}" for real-time updates`);
        return componentId;
    }

    /**
     * Unregister a component from real-time updates
     * @param {number} componentId - Component ID returned from register()
     */
    unregister(componentId) {
        this.stopUpdates(componentId);
        const component = this.updateCallbacks.get(componentId);
        this.updateCallbacks.delete(componentId);
        
        if (component) {
            console.log(`Unregistered component "${component.name}" from real-time updates`);
        }
    }

    /**
     * Start updates for a specific component
     * @param {number} componentId - Component ID
     */
    startUpdates(componentId) {
        this.stopUpdates(componentId); // Clear any existing interval
        
        const component = this.updateCallbacks.get(componentId);
        if (!component) return;
        
        const interval = this.marketStatusService.getUpdateInterval();
        const intervalId = setInterval(() => {
            try {
                component.callback();
            } catch (error) {
                console.error(`Error in update callback for component "${component.name}":`, error);
            }
        }, interval);
        
        this.updateIntervals.set(componentId, intervalId);
        
        const marketStatus = this.marketStatusService.getMarketStatus();
        console.log(`Started updates for "${component.name}" - interval: ${interval/1000}s (market ${marketStatus.status})`);
    }

    /**
     * Stop updates for a specific component
     * @param {number} componentId - Component ID
     */
    stopUpdates(componentId) {
        const intervalId = this.updateIntervals.get(componentId);
        if (intervalId) {
            clearInterval(intervalId);
            this.updateIntervals.delete(componentId);
        }
    }

    /**
     * Handle market status changes
     * @param {object} marketStatus - New market status
     */
    onMarketStatusChange(marketStatus) {
        console.log(`Market status changed to: ${marketStatus.status} - updating all components`);
        
        // Restart all component updates with new interval
        this.updateCallbacks.forEach((component, componentId) => {
            this.startUpdates(componentId);
        });
    }

    /**
     * Cleanup resources
     */
    destroy() {
        // Stop all updates
        this.updateIntervals.forEach((intervalId) => {
            clearInterval(intervalId);
        });
        this.updateIntervals.clear();
        this.updateCallbacks.clear();
        
        // Unsubscribe from market status
        if (this.statusSubscriptionId) {
            this.marketStatusService.unsubscribe(this.statusSubscriptionId);
        }
    }
}

// Create global instances
const marketStatusService = new MarketStatusService();
const realTimeUpdateManager = new RealTimeUpdateManager(marketStatusService);

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        MarketStatusService, 
        RealTimeUpdateManager, 
        MARKET_CONFIG,
        marketStatusService,
        realTimeUpdateManager
    };
} else {
    // Browser globals
    window.MarketStatusService = MarketStatusService;
    window.RealTimeUpdateManager = RealTimeUpdateManager;
    window.MARKET_CONFIG = MARKET_CONFIG;
    window.marketStatusService = marketStatusService;
    window.realTimeUpdateManager = realTimeUpdateManager;
}
