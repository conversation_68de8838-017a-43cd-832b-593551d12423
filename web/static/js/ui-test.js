/**
 * UI Testing and Validation Script
 * Tests the improved UI/UX features of the NewsMonitor application
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

class UITester {
    constructor() {
        this.testResults = [];
        this.init();
    }

    init() {
        console.log('🧪 Starting UI/UX validation tests...');
        this.runAllTests();
    }

    async runAllTests() {
        // Layout and Visual Design Tests
        await this.testMarketOverviewLayout();
        await this.testCompactPriceDisplay();
        await this.testSourceNameFormatting();
        
        // AI Market Analysis Tests
        await this.testMergedMarketComponents();
        await this.testConsistentStyling();
        
        // Real-time Updates Tests
        await this.testRealTimeUpdates();
        
        // JavaScript Refactoring Tests
        await this.testErrorHandling();
        await this.testConfigurationUsage();
        
        // CSS Optimization Tests
        await this.testCommonCSSClasses();
        await this.testResponsiveDesign();
        
        // Report results
        this.reportResults();
    }

    // Layout and Visual Design Tests
    async testMarketOverviewLayout() {
        const test = 'Market Overview Seamless Layout';
        try {
            const marketOverview = document.querySelector('.seamless-market-overview');
            const hasFullWidth = marketOverview && getComputedStyle(marketOverview).width;
            const hasNoMargin = marketOverview && getComputedStyle(marketOverview).margin === '0px';
            
            this.addResult(test, hasFullWidth && hasNoMargin, 
                'Market overview should span full width with no margins');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    async testCompactPriceDisplay() {
        const test = 'Compact Price Display';
        try {
            const tickerItems = document.querySelectorAll('.ticker-item');
            let allCompact = true;
            
            tickerItems.forEach(item => {
                const fontSize = parseFloat(getComputedStyle(item.querySelector('.ticker-value')).fontSize);
                if (fontSize > 16) allCompact = false; // Should be compact (≤16px)
            });
            
            this.addResult(test, allCompact, 
                'All ticker values should have compact font sizes');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    async testSourceNameFormatting() {
        const test = 'Source Name Formatting';
        try {
            // Test the formatSourceName function if available
            if (window.news && typeof window.news.formatSourceName === 'function') {
                const testCases = [
                    { input: 'yahoo_finance', expected: 'Yahoo Finance' },
                    { input: 'reuters', expected: 'Reuters' },
                    { input: 'business_insider', expected: 'Business Insider' }
                ];
                
                let allPassed = true;
                testCases.forEach(testCase => {
                    const result = window.news.formatSourceName(testCase.input);
                    if (result !== testCase.expected) {
                        allPassed = false;
                        console.warn(`Source formatting failed: ${testCase.input} -> ${result} (expected: ${testCase.expected})`);
                    }
                });
                
                this.addResult(test, allPassed, 
                    'Source names should be properly formatted from snake_case to Title Case');
            } else {
                this.addResult(test, false, 'formatSourceName function not available');
            }
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    // AI Market Analysis Tests
    async testMergedMarketComponents() {
        const test = 'Merged Market Components';
        try {
            const compactOverview = document.querySelector('.compact-market-overview-section');
            const predictionSection = document.querySelector('.prediction-section');
            const levelsSection = document.querySelector('.levels-section');
            
            const hasMergedLayout = compactOverview && predictionSection && levelsSection;
            
            this.addResult(test, hasMergedLayout, 
                'Market prediction and critical levels should be merged into compact overview');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    async testConsistentStyling() {
        const test = 'Consistent AI Analysis Styling';
        try {
            const modernCards = document.querySelectorAll('.card-modern');
            const headerIcons = document.querySelectorAll('.card-header-icon');
            
            let consistentStyling = true;
            modernCards.forEach(card => {
                const borderRadius = getComputedStyle(card).borderRadius;
                if (!borderRadius.includes('12px')) consistentStyling = false;
            });
            
            this.addResult(test, consistentStyling && headerIcons.length > 0, 
                'All AI analysis components should have consistent modern styling');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    // Real-time Updates Tests
    async testRealTimeUpdates() {
        const test = 'Real-time Updates Configuration';
        try {
            // Check if MarketAnalysis class has real-time update properties
            const hasRealTimeConfig = window.marketAnalysis && 
                                    window.marketAnalysis.chartUpdateInterval !== undefined &&
                                    window.marketAnalysis.marketStatusInterval !== undefined;
            
            this.addResult(test, hasRealTimeConfig, 
                'Market analysis should have real-time update intervals configured');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    // JavaScript Refactoring Tests
    async testErrorHandling() {
        const test = 'Improved Error Handling';
        try {
            // Check if utility classes exist
            const hasMarketUtils = typeof MarketAnalysisUtils !== 'undefined';
            const hasNewsUtils = typeof NewsUtils !== 'undefined';
            
            // Check if error handling methods exist
            const hasErrorMethods = hasMarketUtils && 
                                  typeof MarketAnalysisUtils.escapeHtml === 'function' &&
                                  hasNewsUtils &&
                                  typeof NewsUtils.sanitizeErrorMessage === 'function';
            
            this.addResult(test, hasErrorMethods, 
                'Utility classes with error handling methods should be available');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    async testConfigurationUsage() {
        const test = 'Configuration Constants Usage';
        try {
            // Check if configuration objects exist
            const hasConfig = typeof CONFIG !== 'undefined';
            const hasNewsConfig = typeof NEWS_CONFIG !== 'undefined';
            
            // Check if they have expected properties
            const hasExpectedProps = hasConfig && 
                                   CONFIG.CHART_COLORS &&
                                   CONFIG.DOM_IDS &&
                                   hasNewsConfig &&
                                   NEWS_CONFIG.API_ENDPOINTS;
            
            this.addResult(test, hasExpectedProps, 
                'Configuration constants should be properly defined and used');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    // CSS Optimization Tests
    async testCommonCSSClasses() {
        const test = 'Common CSS Classes';
        try {
            // Test if common CSS classes are available
            const testElement = document.createElement('div');
            testElement.className = 'flex-center card-modern btn-modern';
            document.body.appendChild(testElement);
            
            const styles = getComputedStyle(testElement);
            const hasFlexCenter = styles.display === 'flex';
            
            document.body.removeChild(testElement);
            
            this.addResult(test, hasFlexCenter, 
                'Common CSS utility classes should be available and functional');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    async testResponsiveDesign() {
        const test = 'Responsive Design';
        try {
            // Test mobile breakpoint behavior
            const originalWidth = window.innerWidth;
            
            // Simulate mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375
            });
            
            // Trigger resize event
            window.dispatchEvent(new Event('resize'));
            
            // Check if mobile-specific styles are applied
            const marketOverview = document.querySelector('.market-overview-content');
            const isMobileLayout = marketOverview && 
                                 getComputedStyle(marketOverview).gridTemplateColumns === '1fr';
            
            // Restore original width
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: originalWidth
            });
            
            this.addResult(test, isMobileLayout, 
                'Responsive design should adapt to mobile viewports');
        } catch (error) {
            this.addResult(test, false, `Error: ${error.message}`);
        }
    }

    // Helper methods
    addResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${message}`);
    }

    reportResults() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(result => result.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n📊 UI/UX Test Results Summary:');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults.filter(result => !result.passed).forEach(result => {
                console.log(`  • ${result.name}: ${result.message}`);
            });
        }
        
        // Store results for potential display in UI
        window.uiTestResults = this.testResults;
    }
}

// Auto-run tests when DOM is loaded (only in development/testing)
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // Wait a bit for other scripts to load
        setTimeout(() => {
            new UITester();
        }, 2000);
    });
}
