/**
 * Price Graph Module
 * Dedicated module for handling interactive price charts and data visualization
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

// Chart Configuration Constants
const CHART_CONFIG = {
    COLORS: {
        PRIMARY: '#007bff',
        VOLUME: 'rgba(255,165,0,0.6)',
        UP: '#10b981',
        DOWN: '#ef4444',
        NEUTRAL: '#6b7280'
    },
    
    DOM_IDS: {
        PRICE_CHART: 'price-chart',
        TICKER_SELECT: 'ticker-select'
    },
    
    PLOTLY_CONFIG: {
        responsive: true,
        displayModeBar: true,
        modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
        displaylogo: false
    }
};

/**
 * Price Chart Class
 * Handles all chart rendering, data processing, and chart-specific interactions
 */
class PriceChart {
    constructor(apiService, templateGenerator) {
        this.apiService = apiService;
        this.templateGenerator = templateGenerator;
        this.currentTicker = 'SPY';
        this.chartData = null;
        this.isLoading = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Ticker selection
        const tickerSelect = document.getElementById(CHART_CONFIG.DOM_IDS.TICKER_SELECT);
        if (tickerSelect) {
            tickerSelect.addEventListener('change', (e) => {
                this.currentTicker = e.target.value;
                this.loadData();
            });
        }
    }

    /**
     * Load chart data for the current ticker
     */
    async loadData() {
        if (this.isLoading) {
            console.log('Chart already loading, skipping...');
            return;
        }
        
        this.isLoading = true;
        
        try {
            console.log(`Loading chart data for ${this.currentTicker}...`);
            this.showLoading();

            // Get date range (1 year)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setFullYear(startDate.getFullYear() - 1);

            console.log(`Fetching data from ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);
            
            const data = await this.apiService.fetchChartData(
                this.currentTicker,
                startDate.toISOString().split('T')[0],
                endDate.toISOString().split('T')[0]
            );

            console.log('Chart data received:', data);
            this.chartData = data;
            await this.render(data);
            console.log('Chart loading completed successfully');

        } catch (error) {
            console.error('Error loading chart data:', error);
            this.showError(error.message);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Render the chart with the provided data
     */
    async render(data) {
        const chartContainer = document.getElementById(CHART_CONFIG.DOM_IDS.PRICE_CHART);
        if (!chartContainer) {
            this.showError('Chart container not found');
            return;
        }

        // Extract data from the API format
        const { dates, close: prices, volume: volumes, ticker } = data;
        
        if (!dates || !prices || dates.length === 0 || prices.length === 0) {
            console.error('Empty or invalid chart data arrays');
            this.showError(`No chart data available for ${ticker || this.currentTicker}`);
            return;
        }

        try {
            // Clear the loading spinner
            chartContainer.innerHTML = '';
            
            const traces = this.createTraces(dates, prices, volumes, ticker);
            const layout = this.createLayout();
            const config = CHART_CONFIG.PLOTLY_CONFIG;

            console.log('Rendering Plotly chart...');
            await Plotly.newPlot(chartContainer, traces, layout, config);
            console.log('Chart rendered successfully');
            
            // Update the price summary
            this.updatePriceSummary(data);
            
        } catch (error) {
            console.error('Error rendering chart:', error);
            this.showError(`Failed to render chart: ${error.message}`);
        }
    }

    /**
     * Create Plotly traces for price and volume data
     */
    createTraces(dates, prices, volumes, ticker) {
        return [
            {
                x: dates,
                y: prices,
                type: 'scatter',
                mode: 'lines',
                name: ticker || this.currentTicker,
                line: { color: CHART_CONFIG.COLORS.PRIMARY, width: 3 },
                yaxis: 'y1',
                hovertemplate: `${ticker || this.currentTicker} Price: $%{y:.2f}<br>Date: %{x}<extra></extra>`,
                showlegend: false
            },
            {
                x: dates,
                y: volumes,
                type: 'bar',
                name: 'Volume',
                marker: { color: CHART_CONFIG.COLORS.VOLUME },
                yaxis: 'y2',
                hovertemplate: 'Volume: %{y:,}<br>Date: %{x}<extra></extra>',
                showlegend: false
            }
        ];
    }

    /**
     * Create Plotly layout configuration
     */
    createLayout() {
        return {
            title: { text: '', font: { size: 16 } },
            xaxis: { title: '', type: 'date', showgrid: false },
            yaxis: { 
                title: 'Price ($)', 
                side: 'left',
                showgrid: true,
                gridcolor: 'rgba(0,0,0,0.1)'
            },
            yaxis2: {
                title: 'Volume',
                side: 'right',
                overlaying: 'y',
                showgrid: false,
                range: [0, null]
            },
            margin: { l: 60, r: 60, t: 30, b: 40 },
            plot_bgcolor: 'rgba(0,0,0,0)',
            paper_bgcolor: 'rgba(0,0,0,0)',
            hovermode: 'x unified',
            showlegend: false
        };
    }

    /**
     * Update price summary display
     */
    updatePriceSummary(data) {
        const { close: prices, ticker } = data;
        if (!prices || prices.length < 2) return;
        
        const currentPrice = prices[prices.length - 1];
        const previousPrice = prices[prices.length - 2];
        const change = currentPrice - previousPrice;
        const changePercent = previousPrice !== 0 ? (change / previousPrice) * 100 : 0;
        
        // Update chart header with current price info
        const chartHeaderElement = document.querySelector('.price-chart-title');
        if (chartHeaderElement) {
            const priceChangeClass = change >= 0 ? 'text-success' : 'text-danger';
            const priceChangeIcon = change >= 0 ? '▲' : '▼';
            
            chartHeaderElement.innerHTML = `
                <i class="bi bi-graph-up"></i>
                ${ticker || this.currentTicker} 
                <small class="${priceChangeClass}">
                    $${currentPrice.toFixed(2)} ${priceChangeIcon} ${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent >= 0 ? '+' : ''}${changePercent.toFixed(2)}%)
                </small>
            `;
        }
    }

    /**
     * Show loading state
     */
    showLoading() {
        const chartContainer = document.getElementById(CHART_CONFIG.DOM_IDS.PRICE_CHART);
        if (chartContainer) {
            chartContainer.innerHTML = this.templateGenerator.generateLoadingSpinner(
                `Loading ${this.currentTicker} price chart...`
            );
        }
    }

    /**
     * Show error state
     */
    showError(message) {
        const chartContainer = document.getElementById(CHART_CONFIG.DOM_IDS.PRICE_CHART);
        if (chartContainer) {
            chartContainer.innerHTML = this.templateGenerator.generateErrorMessage(
                `Error loading chart: ${message}`
            );
        }
    }

    /**
     * Get current ticker
     */
    getCurrentTicker() {
        return this.currentTicker;
    }

    /**
     * Set current ticker
     */
    setCurrentTicker(ticker) {
        this.currentTicker = ticker;
    }

    /**
     * Get chart data
     */
    getChartData() {
        return this.chartData;
    }

    /**
     * Check if chart is currently loading
     */
    isChartLoading() {
        return this.isLoading;
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { PriceChart, CHART_CONFIG };
} else {
    // Browser global
    window.PriceChart = PriceChart;
    window.CHART_CONFIG = CHART_CONFIG;
}
