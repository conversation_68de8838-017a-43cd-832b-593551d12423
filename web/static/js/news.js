/**
 * News JavaScript Module
 * Yahoo Finance-style news layout with featured news and advanced search
 *
 * <AUTHOR> Team
 * @version 2.0.0
 */

// Configuration Constants for News Module
const NEWS_CONFIG = {
    API_ENDPOINTS: {
        FEATURED: '/api/featured-news',
        MOR<PERSON>_NEWS: '/api/more-news',
        SEARCH: '/api/search-news'
    },

    PAGINATION: {
        MORE_ARTICLES_LIMIT: 10,
        INITIAL_OFFSET: 0
    },

    TIMEOUTS: {
        SEARCH_DEBOUNCE: 300,
        API_TIMEOUT: 10000
    },

    DOM_IDS: {
        FEATURED_GRID: 'featured-news-grid',
        MORE_NEWS_LIST: 'more-news-list',
        SEARCH_INPUT: 'news-search-input',
        ADVANCED_FILTERS: 'advanced-filters',
        DATE_FROM: 'date-from',
        DATE_TO: 'date-to',
        NEWS_SOURCE: 'news-source',
        SENTIMENT_FILTER: 'sentiment-filter'
    },

    CSS_CLASSES: {
        LOADING: 'news-loading',
        ERROR: 'news-error',
        FEATURED_ARTICLE: 'featured-article',
        NEWS_LIST_ITEM: 'news-list-item'
    }
};

/**
 * News Utility Functions
 * Reusable utility functions for the news module
 */
class NewsUtils {
    /**
     * Escape HTML to prevent XSS attacks
     * @param {string} text - Text to escape
     * @returns {string} - Escaped HTML
     */
    static escapeHtml(text) {
        if (typeof text !== 'string') return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Format date with time
     * @param {string|Date} date - Date to format
     * @returns {string} - Formatted date string
     */
    static formatDateWithTime(date) {
        if (!date) return 'Unknown date';
        try {
            const dateObj = new Date(date);
            return dateObj.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.warn('Error formatting date:', error);
            return 'Invalid date';
        }
    }

    /**
     * Debounce function to limit function calls
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} - Debounced function
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Check if device is mobile
     * @returns {boolean} - True if mobile device
     */
    static isMobile() {
        return window.innerWidth <= 768;
    }

    /**
     * Sanitize error message for display
     * @param {string} message - Error message
     * @returns {string} - Sanitized message
     */
    static sanitizeErrorMessage(message) {
        if (!message) return 'An unknown error occurred';
        // Remove sensitive information and limit length
        return message.replace(/\b(?:token|key|password|secret)\b/gi, '[REDACTED]').substring(0, 200);
    }
}

class News {
    constructor() {
        // Article data
        this.featuredArticles = [];
        this.moreArticles = [];
        this.displayedMoreArticles = [];

        // Pagination and loading state
        this.moreArticlesOffset = NEWS_CONFIG.PAGINATION.INITIAL_OFFSET;
        this.moreArticlesLimit = NEWS_CONFIG.PAGINATION.MORE_ARTICLES_LIMIT;
        this.isLoading = false;
        this.abortController = null;

        // Filter state
        this.currentFilters = {
            search: '',
            dateFrom: '',
            dateTo: '',
            source: '',
            sentiment: ''
        };

        // Debounced search function
        this.debouncedSearch = NewsUtils.debounce(
            this.performSearch.bind(this),
            NEWS_CONFIG.TIMEOUTS.SEARCH_DEBOUNCE
        );

        this.init();
    }

    /**
     * Format source name from snake_case to Title Case
     * @param {string} source - The source name to format
     * @returns {string} - Formatted source name
     */
    formatSourceName(source) {
        if (!source) return 'Unknown';

        // Handle special cases
        const specialCases = {
            'yahoo_finance': 'Yahoo Finance',
            'reuters': 'Reuters',
            'bloomberg': 'Bloomberg',
            'cnbc': 'CNBC',
            'marketwatch': 'MarketWatch',
            'wall_street_journal': 'Wall Street Journal',
            'financial_times': 'Financial Times',
            'seeking_alpha': 'Seeking Alpha',
            'motley_fool': 'The Motley Fool',
            'barrons': "Barron's",
            'forbes': 'Forbes',
            'cnn_business': 'CNN Business',
            'fox_business': 'Fox Business',
            'business_insider': 'Business Insider'
        };

        const lowerSource = source.toLowerCase();
        if (specialCases[lowerSource]) {
            return specialCases[lowerSource];
        }

        // Convert snake_case to Title Case
        return source
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
            .join(' ');
    }

    init() {
        this.setupEventListeners();
        this.addNewsMetaStyles();
        this.loadNews();
    }

    setupEventListeners() {
        try {
            // Search functionality with debouncing
            const searchInput = document.getElementById(NEWS_CONFIG.DOM_IDS.SEARCH_INPUT);
            const searchButton = document.getElementById('search-button');

            if (searchInput) {
                searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.performSearch();
                    }
                });

                // Add real-time search with debouncing
                searchInput.addEventListener('input', (e) => {
                    if (e.target.value.trim() !== this.currentFilters.search) {
                        this.debouncedSearch();
                    }
                });
            }

            if (searchButton) {
                searchButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.performSearch();
                });
            }

        // Advanced search toggle
        const toggleAdvanced = document.getElementById('toggle-advanced-search');
        const advancedFilters = document.getElementById('advanced-filters');
        
        if (toggleAdvanced && advancedFilters) {
            toggleAdvanced.addEventListener('click', (e) => {
                e.preventDefault();
                advancedFilters.classList.toggle('show');
                const icon = toggleAdvanced.querySelector('i');
                if (icon) {
                    if (advancedFilters.classList.contains('show')) {
                        icon.className = 'bi bi-chevron-up';
                        toggleAdvanced.setAttribute('aria-expanded', 'true');
                    } else {
                        icon.className = 'bi bi-sliders';
                        toggleAdvanced.setAttribute('aria-expanded', 'false');
                    }
                }
            });
        }

        // Filter controls
        const applyFiltersBtn = document.getElementById('apply-filters');
        const clearFiltersBtn = document.getElementById('clear-filters');
        
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.applyFilters();
            });
        }
        
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.clearFilters();
            });
        }
        
            // Handle page visibility changes
            document.addEventListener('visibilitychange', () => {
                if (document.hidden && this.abortController) {
                    this.abortController.abort();
                }
            });
        } catch (error) {
            console.error('Error setting up event listeners:', error);
        }
    }

    async loadNews() {
        if (this.isLoading) {
            console.log('News loading already in progress');
            return;
        }
        
        try {
            this.isLoading = true;
            this.abortController = new AbortController();
            this.showLoading();

            // Load featured news and more news separately with timeout
            await Promise.allSettled([
                this.loadFeaturedNews(),
                this.loadMoreNews()
            ]);

        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('Error loading news:', error);
                this.showError(NewsUtils.sanitizeErrorMessage(error.message));
            }
        } finally {
            this.isLoading = false;
            this.abortController = null;
        }
    }

    async loadFeaturedNews() {
        try {
            const response = await fetch(`${NEWS_CONFIG.API_ENDPOINTS.FEATURED}?limit=6`, {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('Featured news endpoint not found');
                } else if (response.status >= 500) {
                    throw new Error('Server error occurred');
                } else {
                    const data = await response.json().catch(() => ({}));
                    throw new Error(data.error || `Request failed with status ${response.status}`);
                }
            }
            
            const data = await response.json();
            this.featuredArticles = Array.isArray(data.all) ? data.all : [];
            this.renderFeaturedNews();

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Featured news request aborted');
                return;
            }
            
            console.error('Error loading featured news:', error);
            
            // Fallback to regular news API
            try {
                const fallbackResponse = await fetch('/api/news?limit=6', {
                    signal: this.abortController?.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    this.featuredArticles = Array.isArray(fallbackData.all) ? fallbackData.all : [];
                    this.renderFeaturedNews();
                } else {
                    throw new Error('Fallback request also failed');
                }
            } catch (fallbackError) {
                if (fallbackError.name !== 'AbortError') {
                    this.showFeaturedError(this.sanitizeErrorMessage(error.message));
                }
            }
        }
    }

    async loadMoreNews() {
        try {
            // Get IDs of featured articles to exclude
            const excludeIds = this.featuredArticles
                .filter(article => article && article.id)
                .map(article => article.id);
                
            const params = new URLSearchParams({
                limit: '20'
            });

            // Add exclude IDs as separate parameters
            excludeIds.forEach(id => {
                if (id != null) {
                    params.append('exclude_ids', String(id));
                }
            });

            const response = await fetch(`${NEWS_CONFIG.API_ENDPOINTS.MORE_NEWS}?${params.toString()}`, {
                signal: this.abortController?.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('More news endpoint not found');
                } else if (response.status >= 500) {
                    throw new Error('Server error occurred');
                } else {
                    const data = await response.json().catch(() => ({}));
                    throw new Error(data.error || `Request failed with status ${response.status}`);
                }
            }
            
            const data = await response.json();
            this.moreArticles = Array.isArray(data.all) ? data.all : [];
            this.renderMoreNews();

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('More news request aborted');
                return;
            }
            
            console.error('Error loading more news:', error);
            
            // Fallback to regular news API
            try {
                const fallbackResponse = await fetch('/api/news?limit=20', {
                    signal: this.abortController?.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (fallbackResponse.ok) {
                    const fallbackData = await fallbackResponse.json();
                    this.moreArticles = Array.isArray(fallbackData.all) ? fallbackData.all : [];
                    this.renderMoreNews();
                } else {
                    throw new Error('Fallback request also failed');
                }
            } catch (fallbackError) {
                if (fallbackError.name !== 'AbortError') {
                    this.showMoreNewsError(this.sanitizeErrorMessage(error.message));
                }
            }
        }
    }



    renderFeaturedNews() {
        const container = document.getElementById('featured-news-grid');

        if (!container) {
            console.warn('Featured news grid container not found');
            return;
        }

        if (!Array.isArray(this.featuredArticles) || this.featuredArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No featured news available</div>';
            return;
        }

        try {
            // Take up to 6 articles for the 2x3 grid
            const gridArticles = this.featuredArticles.slice(0, 6).filter(Boolean);
            
            const articlesHtml = gridArticles.map(article => {
                const sentiment = this.getSentimentFromArticle(article);
                const sentimentClass = sentiment ? `sentiment-${this.sanitizeClassName(sentiment.toLowerCase())}` : '';
                const sentimentBadge = sentiment ? `<span class="sentiment-badge ${sentimentClass}">${this.escapeHtml(sentiment)}</span>` : '';

                return this.createArticleHtml({
                    article,
                    className: 'featured-article',
                    sentimentBadge,
                    contentLength: 120,
                    showInfluence: true
                });
            }).join('');

            container.innerHTML = articlesHtml;
        } catch (error) {
            console.error('Error rendering featured articles:', error);
            container.innerHTML = '<div class="news-error">Error displaying featured articles</div>';
        }
    }

    renderMoreNews() {
        const container = document.getElementById('more-news-list');
        if (!container) return;

        if (this.moreArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Load initial batch
        this.loadMoreArticles(true);
    }

    loadMoreArticles(reset = false) {
        const container = document.getElementById('more-news-list');
        if (!container) {
            console.warn('More news container not found');
            return;
        }

        if (reset) {
            this.moreArticlesOffset = 0;
            this.displayedMoreArticles = [];
            container.innerHTML = '';
        }

        if (!Array.isArray(this.moreArticles)) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Get next batch of articles
        const nextBatch = this.moreArticles.slice(
            this.moreArticlesOffset,
            this.moreArticlesOffset + this.moreArticlesLimit
        ).filter(Boolean);

        if (nextBatch.length === 0 && this.displayedMoreArticles.length === 0) {
            container.innerHTML = '<div class="news-error">No more news available</div>';
            return;
        }

        // Add articles to displayed list
        this.displayedMoreArticles.push(...nextBatch);
        this.moreArticlesOffset += nextBatch.length;

        try {
            // Render articles
            const articlesHtml = this.displayedMoreArticles.map(article => {
                const imageHtml = article.image_url ?
                    `<img src="${this.escapeHtml(article.image_url)}" alt="${this.escapeHtml(article.title || 'News image')}" class="news-list-image clickable-news-image" onclick="window.news?.openArticle('${this.escapeHtml(article.url || '')}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${this.escapeHtml(article.url || '')}')" tabindex="0" onerror="this.style.display='none'" loading="lazy" style="cursor: pointer;">` :
                    `<div class="news-list-image"></div>`;
                    
                const contentHtml = article.content ?
                    `<p class="news-list-summary">${this.escapeHtml(article.content.substring(0, 250))}...</p>` :
                    '';
                const influence = article.article_metadata.influence_tagging;
                const tags = Array.isArray(influence.tags) ? influence.tags : [];
                const category = this.escapeHtml(influence.category || '');
                const regions = Array.isArray(influence.regions) ? influence.regions : [];
                const tagsHtml = tags.slice(0, 3).map(tag => this.escapeHtml(tag)).join(', ');
                const regionsText = regions.map(region => this.escapeHtml(region)).join(', ');
                const categoryText = category || '';
                return `
                    <div class="news-list-item">
                        ${imageHtml}
                        <div class="news-list-content">
                            <h6 class="news-list-title clickable-news-title" onclick="window.news?.openArticle('${this.escapeHtml(article.url || '')}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${this.escapeHtml(article.url || '')}')" tabindex="0" style="cursor: pointer;">${this.escapeHtml(article.title || 'Untitled')}</h6>
                            <div class="news-list-meta">
                                <div class="news-meta-primary">
                                    <span class="news-list-source">${this.escapeHtml(this.formatSourceName(article.source))}</span>
                                    <span class="meta-separator">•</span>
                                    <span class="news-list-date">${this.formatDateWithTime(article.date || article.publish_time)}</span>
                                </div>
                                ${this.isMobile() ? 
                                    `<div class="news-meta-secondary">
                                        ${categoryText ? `<span class="meta-category">${categoryText}</span>` : ''}
                                        ${regionsText ? `<span class="meta-regions">${regionsText}</span>` : ''}
                                        ${tagsHtml ? `<div class="meta-tags">${tagsHtml}</div>` : ''}
                                    </div>` :
                                    `<div class="news-meta-secondary">
                                        ${categoryText ? `<span class="meta-separator">•</span><span class="meta-category">${categoryText}</span>` : ''}
                                        ${regionsText ? `<span class="meta-separator">•</span><span class="meta-regions">${regionsText}</span>` : ''}
                                        ${tagsHtml ? `<span class="meta-separator">•</span><span class="meta-tags">${tagsHtml}</span>` : ''}
                                    </div>`
                                }
                            </div>
                            ${contentHtml}
                        </div>
                    </div>
                `;
            }).join('');

            // Add load more button if there are more articles
            const loadMoreHtml = this.moreArticlesOffset < this.moreArticles.length ?
                `<div class="load-more-container">
                    <button class="load-more-btn" onclick="window.news?.loadMoreArticles()" type="button">
                        Load More Articles
                    </button>
                </div>` : '';

            container.innerHTML = articlesHtml + loadMoreHtml;
        } catch (error) {
            console.error('Error rendering more articles:', error);
            container.innerHTML = '<div class="news-error">Error displaying articles</div>';
        }
    }

    performSearch() {
        const searchInput = document.getElementById('news-search-input');
        if (searchInput) {
            this.currentFilters.search = searchInput.value.trim();
            this.applyFilters();
        }
    }

    applyFilters() {
        // Get filter values
        this.currentFilters.dateFrom = document.getElementById('date-from')?.value || '';
        this.currentFilters.dateTo = document.getElementById('date-to')?.value || '';
        this.currentFilters.source = document.getElementById('news-source')?.value || '';
        this.currentFilters.sentiment = document.getElementById('sentiment-filter')?.value || '';

        // Build API parameters
        const params = new URLSearchParams();
        if (this.currentFilters.search) params.append('search', this.currentFilters.search);
        if (this.currentFilters.dateFrom) params.append('start_date', this.currentFilters.dateFrom);
        if (this.currentFilters.dateTo) params.append('end_date', this.currentFilters.dateTo);
        const limit = this.isMobile() ? 25 : 50;
        params.append('limit', limit.toString());

        // Fetch filtered news
        this.loadFilteredNews(params);
    }

    async loadFilteredNews(params) {
        try {
            this.showLoading();
            
            const response = await fetch(`/api/news?${params.toString()}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Failed to load filtered news');
            }
            
            this.allArticles = data.all || [];
            
            // Apply client-side filters
            this.allArticles = this.allArticles.filter(article => {
                // Source filter
                if (this.currentFilters.source && article.source.toLowerCase() !== this.currentFilters.source.toLowerCase()) {
                    return false;
                }
                
                // Sentiment filter
                if (this.currentFilters.sentiment) {
                    const sentiment = this.getSentimentFromArticle(article);
                    if (!sentiment || sentiment.toLowerCase() !== this.currentFilters.sentiment.toLowerCase()) {
                        return false;
                    }
                }
                
                return true;
            });
            
            this.processArticles();
            this.renderNews();
            
        } catch (error) {
            console.error('Error loading filtered news:', error);
            this.showError(error.message);
        }
    }

    clearFilters() {
        try {
            // Clear all filter inputs safely
            const elements = [
                'news-search-input',
                'date-from', 
                'date-to',
                'news-source',
                'sentiment-filter'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.value = '';
                }
            });
            
            // Reset filters and reload
            this.currentFilters = {
                search: '',
                dateFrom: '',
                dateTo: '',
                source: '',
                sentiment: ''
            };
            
            this.loadNews();
        } catch (error) {
            console.error('Error clearing filters:', error);
        }
    }

    getSentimentFromArticle(article) {
        if (article.article_metadata && article.article_metadata.sentiment_analysis) {
            return article.article_metadata.sentiment_analysis.label;
        }
        return null;
    }

    formatDate(dateString) {
        if (!dateString) return 'Recent';

        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) return '1 day ago';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;

        return date.toLocaleDateString();
    }

    formatDateWithTime(dateString) {
        if (!dateString) return 'Recent';

        const date = new Date(dateString);

        // Convert to EST timezone
        const estDate = new Date(date.toLocaleString("en-US", {timeZone: "America/New_York"}));

        const options = {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
            timeZone: 'America/New_York'
        };

        return estDate.toLocaleDateString('en-US', options) + ' EST';
    }

    renderInfluenceTagging(article) {
        try {
            if (!article?.article_metadata?.influence_tagging) {
                return '';
            }
            // Full version for main article
            const score = article.article_metadata.influence_tagging.influence;
            const category = this.escapeHtml(article.article_metadata.influence_tagging.category || 'N/A');
            const regions = Array.isArray(article.article_metadata.influence_tagging.regions) ? article.article_metadata.influence_tagging.regions : [];
            const tags = Array.isArray(article.article_metadata.influence_tagging.tags) ? article.article_metadata.influence_tagging.tags : [];
            const reason = this.escapeHtml(article.article_metadata.influence_tagging.reason || '');
            const scoreClass = score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral';
            const regionsText = regions.map(region => this.escapeHtml(region)).join(', ');
            const tooltipAttr = reason ? `data-tooltip="${reason}"` : '';
            const tooltipClass = reason ? 'has-tooltip' : '';
            const tagsHtml = tags.slice(0, 5).map(tag => `<span class="influence-tag">${this.escapeHtml(tag)}</span>`).join('');

            return `
                <div class="influence-tags">
                    <div class="influence-tags-list">
                        ${tagsHtml}
                    </div>
                    <div class="influence-meta">
                        <span><strong>Category:</strong> ${category} | <strong>Region:</strong> ${regionsText}</span>
                        <span class="influence-score ${scoreClass} ${tooltipClass}" ${tooltipAttr}>${score > 0 ? '+' : ''}${score}</span>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('Error rendering influence tagging:', error);
            return '';
        }
    }

    showLoading() {
        const featuredContainer = document.getElementById('featured-news-grid');
        const moreContainer = document.getElementById('more-news-list');
        
        const loadingHtml = `
            <div class="news-loading">
                <div class="news-loading-spinner"></div>
                <p>Loading news...</p>
            </div>
        `;
        
        if (featuredContainer) featuredContainer.innerHTML = loadingHtml;
        if (moreContainer) moreContainer.innerHTML = loadingHtml;
    }

    showError(message) {
        this.showFeaturedError(message);
        this.showMoreNewsError(message);
    }

    showFeaturedError(message) {
        const container = document.getElementById('featured-news-grid');

        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading featured news: ${this.escapeHtml(message)}
            </div>
        `;

        if (container) container.innerHTML = errorHtml;
    }

    showMoreNewsError(message) {
        const moreContainer = document.getElementById('more-news-list');

        const errorHtml = `
            <div class="news-error">
                <i class="bi bi-exclamation-triangle"></i>
                Error loading more news: ${this.escapeHtml(message)}
            </div>
        `;

        if (moreContainer) moreContainer.innerHTML = errorHtml;
    }
    
    // Utility functions for improved security and functionality
    escapeHtml(text) {
        if (typeof text !== 'string') return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    sanitizeClassName(className) {
        if (typeof className !== 'string') return '';
        return className.replace(/[^a-z0-9-_]/gi, '');
    }
    
    sanitizeErrorMessage(message) {
        if (typeof message !== 'string') return 'Unknown error occurred';
        // Remove potentially sensitive information
        return message.replace(/https?:\/\/[^\s]+/gi, '[URL]')
                     .replace(/\b(?:\d{1,3}\.){3}\d{1,3}\b/g, '[IP]')
                     .substring(0, 200); // Limit length
    }
    
    createArticleHtml({ article, className, sentimentBadge, contentLength, showInfluence }) {
        if (!article) return '';
        
        const title = this.escapeHtml(article.title || 'Untitled');
        const source = this.escapeHtml(this.formatSourceName(article.source));
        const url = this.escapeHtml(article.url || '');
        const imageUrl = this.escapeHtml(article.image_url || '');
        
        const imageHtml = imageUrl ?
            `<img src="${imageUrl}" alt="${title}" class="featured-article-image clickable-news-image" onclick="window.news?.openArticle('${url}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${url}')" tabindex="0" onerror="this.style.display='none'" loading="lazy" style="cursor: pointer;">` :
            `<div class="featured-article-image"></div>`;
            
        const contentHtml = article.content ?
            `<p class="featured-article-summary">${this.escapeHtml(article.content.substring(0, contentLength))}...</p>` :
            '';
            
        const influenceHtml = showInfluence ? this.renderInfluenceTagging(article) : '';
        
        return `
            <div class="${className}">
                ${imageHtml}
                <div class="featured-article-content">
                    <h6 class="featured-article-title clickable-news-title" onclick="window.news?.openArticle('${url}')" onkeypress="if(event.key==='Enter') window.news?.openArticle('${url}')" tabindex="0" style="cursor: pointer;">${title}</h6>
                    <div class="featured-article-meta">
                        <span class="featured-article-source">${source}</span>
                        <span class="news-list-date">${this.formatDateWithTime(article.date || article.publish_time)}</span>
                        ${sentimentBadge}
                    </div>
                    ${contentHtml}
                    ${influenceHtml}
                </div>
            </div>
        `;
    }
    
    openArticle(url) {
        if (url && typeof url === 'string') {
            try {
                // Validate URL before opening
                const urlObj = new URL(url);
                if (urlObj.protocol === 'http:' || urlObj.protocol === 'https:') {
                    window.open(url, '_blank', 'noopener,noreferrer');
                }
            } catch (error) {
                console.error('Invalid URL:', error);
            }
        }
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    // Mobile utility functions
    isMobile() {
        return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }
    
    // Add mobile-friendly CSS styles for news meta
    addNewsMetaStyles() {
        if (document.getElementById('news-meta-mobile-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'news-meta-mobile-styles';
        style.textContent = `
            /* Mobile styles for news meta */
            @media (max-width: 768px) {
                .news-list-meta {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    font-size: 12px;
                }
                
                .news-meta-primary {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    font-weight: 600;
                    color: #374151;
                }
                
                .news-meta-secondary {
                    display: flex;
                    flex-direction: column;
                    gap: 3px;
                }
                
                .news-meta-secondary .meta-separator {
                    display: none;
                }
                
                .meta-category,
                .meta-regions {
                    font-size: 11px;
                    color: #64748b;
                    background: #f1f5f9;
                    padding: 2px 6px;
                    border-radius: 4px;
                    display: inline-block;
                    margin-right: 4px;
                    margin-bottom: 2px;
                    border: 1px solid #e2e8f0;
                }
                
                .meta-tags {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 2px;
                    margin-top: 2px;
                }
                
                .meta-tags span {
                    font-size: 10px;
                    background: #e2e8f0;
                    color: #475569;
                    padding: 1px 4px;
                    border-radius: 3px;
                    max-width: 80px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    border: 1px solid #cbd5e1;
                }
                
                .news-list-source {
                    font-weight: 600;
                    color: #1e293b;
                }
                
                .news-list-date {
                    color: #64748b;
                    font-size: 11px;
                }
                
                .meta-separator {
                    color: #94a3b8;
                    margin: 0 2px;
                }
            }
            
            /* Desktop styles for news meta */
            @media (min-width: 769px) {
                .news-list-meta {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                    flex-wrap: wrap;
                    font-size: 12px;
                    color: #64748b;
                }
                
                .news-meta-primary,
                .news-meta-secondary {
                    display: flex;
                    align-items: center;
                    gap: 6px;
                }
                
                .meta-separator {
                    color: #94a3b8;
                    font-size: 12px;
                }
                
                .meta-category,
                .meta-regions,
                .meta-tags {
                    font-size: 12px;
                    color: #64748b;
                }
                
                .meta-tags span {
                    margin-right: 4px;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        window.news = new News();
    } catch (error) {
        console.error('Failed to initialize News:', error);
    }
});

// Handle page unload to cleanup
window.addEventListener('beforeunload', function() {
    if (window.news?.abortController) {
        window.news.abortController.abort();
    }
});
