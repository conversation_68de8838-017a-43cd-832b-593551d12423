/**
 * Market Overview JavaScript Module
 * Handles the market overview banner with real-time price data
 */

class MarketOverview {
    constructor() {
        this.tickers = ['SPY', 'VOO', '^GSPC', '^DJI', '^IXIC'];
        this.elementPrefixes = {
            'SPY': 'spy',
            'VOO': 'voo',
            '^GSPC': 'sp500',
            '^DJI': 'dow',
            '^IXIC': 'nasdaq'
        };
        this.priceData = {};
        this.updateManagerId = null;
        this.statusSubscriptionId = null;
        
        this.init();
    }

    init() {
        this.loadMarketData();
        this.setupMarketHoursMonitoring();
    }

    async loadMarketData() {
        try {
            const marketStatus = marketStatusService.getMarketStatus();
            console.log(`Loading market data - Market ${marketStatus.isOpen ? 'OPEN' : 'CLOSED'} (${marketStatus.status})`);

            const response = await fetch('/api/market-overview');
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to load market data');
            }

            // Store the market data
            this.priceData = data;
            this.updateDisplay();

        } catch (error) {
            console.error('Error loading market data:', error);
            this.showError();
        }
    }

    updateDisplay() {
        this.tickers.forEach(ticker => {
            const elementPrefix = this.elementPrefixes[ticker];
            this.updateMetric(elementPrefix, ticker);
        });
    }

    updateMetric(elementPrefix, dataKey) {
        const data = this.priceData[dataKey];
        if (!data) return;

        const priceElement = document.getElementById(`${elementPrefix}-price`);
        const changeElement = document.getElementById(`${elementPrefix}-change`);

        if (priceElement) {
            if (dataKey.startsWith('^')) {
                // Index values (no dollar sign, show as whole numbers)
                priceElement.textContent = data.price.toFixed(0);
            } else {
                // ETF prices (show with dollar sign and 2 decimals)
                priceElement.textContent = `$${data.price.toFixed(2)}`;
            }
        }

        if (changeElement) {
            const changeText = `${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.changePercent >= 0 ? '+' : ''}${data.changePercent.toFixed(2)}%)`;
            changeElement.textContent = changeText;

            // Update color based on change
            changeElement.className = `metric-change ${data.change >= 0 ? 'positive' : 'negative'}`;
        }
    }

    showError() {
        // Show error state for all metrics
        const metrics = ['sp500', 'dow', 'nasdaq', 'spy', 'voo'];
        
        metrics.forEach(metric => {
            const priceElement = document.getElementById(`${metric}-price`);
            const changeElement = document.getElementById(`${metric}-change`);
            
            if (priceElement) priceElement.textContent = 'Error';
            if (changeElement) {
                changeElement.textContent = 'Unable to load';
                changeElement.className = 'metric-change';
            }
        });
    }

    destroy() {
        if (this.updateManagerId) {
            realTimeUpdateManager.unregister(this.updateManagerId);
        }
        if (this.statusSubscriptionId) {
            marketStatusService.unsubscribe(this.statusSubscriptionId);
        }
    }
    

    
    /**
     * Format time duration in a human-readable format
     * @param {number} milliseconds
     * @returns {string} Formatted time string
     */
    formatTimeUntil(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        
        if (hours > 24) {
            const days = Math.floor(hours / 24);
            const remainingHours = hours % 24;
            return `${days}d ${remainingHours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
    
    /**
     * Setup market hours monitoring using shared utilities
     */
    setupMarketHoursMonitoring() {
        // Subscribe to market status changes
        this.statusSubscriptionId = marketStatusService.subscribe((marketStatus) => {
            this.updateMarketStatusDisplay(marketStatus);
        });

        // Register for real-time updates with faster interval for market overview
        this.updateManagerId = realTimeUpdateManager.register(
            () => {
                this.loadMarketData();
            },
            'Market Overview'
        );
    }
    
    /**
     * Update the market status display in the UI
     * @param {object} marketStatus
     */
    updateMarketStatusDisplay(marketStatus) {
        // Find or create market status element
        let statusElement = document.getElementById('market-status');
        if (!statusElement) {
            // Create status element if it doesn't exist
            const container = document.querySelector('.market-overview') || document.querySelector('.market-banner');
            if (container) {
                statusElement = document.createElement('div');
                statusElement.id = 'market-status';
                statusElement.className = 'market-status';
                container.appendChild(statusElement);
            }
        }

        if (statusElement) {
            const statusClass = marketStatus.isOpen ? 'market-open' : 'market-closed';
            const statusText = marketStatus.isOpen ? 'Market Open' : 'Market Closed';

            statusElement.className = `market-status ${statusClass}`;
            statusElement.innerHTML = `
                <div class="market-status-main">
                    <span class="market-status-indicator"></span>
                    <span class="market-status-text">${statusText}</span>
                </div>
                <div class="market-status-details">
                    <div class="market-time">${marketStatus.currentTime}</div>
                    <div class="market-next">Status: ${marketStatus.status}</div>
                </div>
            `;
        }

        // Add CSS styles if not already present
        this.addMarketStatusStyles();
    }
    
    /**
     * Add CSS styles for market status display
     */
    addMarketStatusStyles() {
        if (document.getElementById('market-status-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'market-status-styles';
        style.textContent = `
            .market-status {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                margin: 8px 0;
                border-radius: 6px;
                font-size: 12px;
                border: 1px solid #e2e8f0;
            }
            
            .market-status.market-open {
                background-color: #f0fdf4;
                border-color: #16a34a;
                color: #166534;
            }
            
            .market-status.market-closed {
                background-color: #fef2f2;
                border-color: #dc2626;
                color: #991b1b;
            }
            
            .market-status-main {
                display: flex;
                align-items: center;
                gap: 6px;
            }
            
            .market-status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
            }
            
            .market-open .market-status-indicator {
                background-color: #16a34a;
                animation: pulse 2s infinite;
            }
            
            .market-closed .market-status-indicator {
                background-color: #dc2626;
            }
            
            .market-status-text {
                font-weight: 600;
            }
            
            .market-status-details {
                text-align: right;
                font-size: 11px;
                opacity: 0.8;
            }
            
            .market-time {
                margin-bottom: 2px;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            
            @media (max-width: 768px) {
                .market-status {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 4px;
                }
                
                .market-status-details {
                    text-align: left;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketOverview = new MarketOverview();
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.marketOverview) {
        window.marketOverview.destroy();
    }
});
