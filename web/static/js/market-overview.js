/**
 * Market Overview JavaScript Module
 * Handles the market overview banner with real-time price data
 */

class MarketOverview {
    constructor() {
        this.tickers = ['SPY', 'VOO', '^GSPC', '^DJI', '^IXIC'];
        this.elementPrefixes = {
            'SPY': 'spy',
            'VOO': 'voo',
            '^GSPC': 'sp500',
            '^DJI': 'dow',
            '^IXIC': 'nasdaq'
        };
        this.priceData = {};
        this.updateInterval = null;
        this.marketStatusInterval = null;
        this.lastMarketStatus = null;
        
        // Market hours (ET): 9:30 AM - 4:00 PM, Monday-Friday
        this.marketOpen = { hour: 9, minute: 30 };
        this.marketClose = { hour: 16, minute: 0 };
        
        this.init();
    }

    init() {
        this.loadMarketData();
        this.setupMarketHoursMonitoring();
    }

    async loadMarketData() {
        try {
            const marketStatus = this.getMarketStatus();
            console.log(`Loading market data - Market ${marketStatus.isOpen ? 'OPEN' : 'CLOSED'} (${marketStatus.status})`);
            
            const response = await fetch('/api/market-overview');
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Failed to load market data');
            }

            // Store the market data
            this.priceData = data;
            this.updateDisplay();

        } catch (error) {
            console.error('Error loading market data:', error);
            this.showError();
        }
    }

    updateDisplay() {
        this.tickers.forEach(ticker => {
            const elementPrefix = this.elementPrefixes[ticker];
            this.updateMetric(elementPrefix, ticker);
        });
    }

    updateMetric(elementPrefix, dataKey) {
        const data = this.priceData[dataKey];
        if (!data) return;

        const priceElement = document.getElementById(`${elementPrefix}-price`);
        const changeElement = document.getElementById(`${elementPrefix}-change`);

        if (priceElement) {
            if (dataKey.startsWith('^')) {
                // Index values (no dollar sign, show as whole numbers)
                priceElement.textContent = data.price.toFixed(0);
            } else {
                // ETF prices (show with dollar sign and 2 decimals)
                priceElement.textContent = `$${data.price.toFixed(2)}`;
            }
        }

        if (changeElement) {
            const changeText = `${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.changePercent >= 0 ? '+' : ''}${data.changePercent.toFixed(2)}%)`;
            changeElement.textContent = changeText;

            // Update color based on change
            changeElement.className = `metric-change ${data.change >= 0 ? 'positive' : 'negative'}`;
        }
    }

    showError() {
        // Show error state for all metrics
        const metrics = ['sp500', 'dow', 'nasdaq', 'spy', 'voo'];
        
        metrics.forEach(metric => {
            const priceElement = document.getElementById(`${metric}-price`);
            const changeElement = document.getElementById(`${metric}-change`);
            
            if (priceElement) priceElement.textContent = 'Error';
            if (changeElement) {
                changeElement.textContent = 'Unable to load';
                changeElement.className = 'metric-change';
            }
        });
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        if (this.marketStatusInterval) {
            clearInterval(this.marketStatusInterval);
        }
    }
    
    /**
     * Check if current time is during US market hours (9:30 AM - 4:00 PM ET, Mon-Fri)
     * @returns {boolean} True if market is open
     */
    isMarketOpen() {
        const now = new Date();
        
        // Convert to New York timezone
        const nyTime = new Date(now.toLocaleString("en-US", {timeZone: "America/New_York"}));
        
        // Check if it's a weekday (Monday = 1, Sunday = 0)
        const dayOfWeek = nyTime.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            return false; // Weekend
        }
        
        // Check if current time is within market hours
        const currentHour = nyTime.getHours();
        const currentMinute = nyTime.getMinutes();
        const currentTimeInMinutes = currentHour * 60 + currentMinute;
        
        const marketOpenInMinutes = this.marketOpen.hour * 60 + this.marketOpen.minute;
        const marketCloseInMinutes = this.marketClose.hour * 60 + this.marketClose.minute;
        
        return currentTimeInMinutes >= marketOpenInMinutes && currentTimeInMinutes < marketCloseInMinutes;
    }
    
    /**
     * Get market status with time information
     * @returns {object} Market status details
     */
    getMarketStatus() {
        const now = new Date();
        const nyTime = new Date(now.toLocaleString("en-US", {timeZone: "America/New_York"}));
        const isOpen = this.isMarketOpen();
        
        const dayOfWeek = nyTime.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        let status, nextEvent, timeUntilNext;
        
        if (isOpen) {
            status = 'open';
            nextEvent = 'Market Close';
            
            // Calculate time until market close
            const closeTime = new Date(nyTime);
            closeTime.setHours(this.marketClose.hour, this.marketClose.minute, 0, 0);
            timeUntilNext = closeTime - nyTime;
        } else if (isWeekend) {
            status = 'weekend';
            nextEvent = 'Market Open';
            
            // Calculate time until next Monday 9:30 AM
            const nextMonday = new Date(nyTime);
            const daysUntilMonday = (8 - dayOfWeek) % 7 || 7;
            nextMonday.setDate(nyTime.getDate() + daysUntilMonday);
            nextMonday.setHours(this.marketOpen.hour, this.marketOpen.minute, 0, 0);
            timeUntilNext = nextMonday - nyTime;
        } else {
            // Weekday but market closed
            const currentHour = nyTime.getHours();
            const currentMinute = nyTime.getMinutes();
            const currentTimeInMinutes = currentHour * 60 + currentMinute;
            const marketOpenInMinutes = this.marketOpen.hour * 60 + this.marketOpen.minute;
            
            if (currentTimeInMinutes < marketOpenInMinutes) {
                // Before market open
                status = 'premarket';
                nextEvent = 'Market Open';
                
                const openTime = new Date(nyTime);
                openTime.setHours(this.marketOpen.hour, this.marketOpen.minute, 0, 0);
                timeUntilNext = openTime - nyTime;
            } else {
                // After market close
                status = 'aftermarket';
                nextEvent = 'Market Open';
                
                // Calculate time until next day's open
                const nextOpen = new Date(nyTime);
                nextOpen.setDate(nyTime.getDate() + 1);
                nextOpen.setHours(this.marketOpen.hour, this.marketOpen.minute, 0, 0);
                
                // If next day is weekend, move to Monday
                if (nextOpen.getDay() === 6) { // Saturday
                    nextOpen.setDate(nextOpen.getDate() + 2);
                } else if (nextOpen.getDay() === 0) { // Sunday
                    nextOpen.setDate(nextOpen.getDate() + 1);
                }
                
                timeUntilNext = nextOpen - nyTime;
            }
        }
        
        return {
            isOpen,
            status,
            nextEvent,
            timeUntilNext,
            nyTime: nyTime.toLocaleString('en-US', {
                timeZone: 'America/New_York',
                weekday: 'short',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                timeZoneName: 'short'
            })
        };
    }
    
    /**
     * Format time duration in a human-readable format
     * @param {number} milliseconds
     * @returns {string} Formatted time string
     */
    formatTimeUntil(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        
        if (hours > 24) {
            const days = Math.floor(hours / 24);
            const remainingHours = hours % 24;
            return `${days}d ${remainingHours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    }
    
    /**
     * Setup market hours monitoring and update intervals
     */
    setupMarketHoursMonitoring() {
        const updateMarketStatus = () => {
            const marketStatus = this.getMarketStatus();
            const isOpen = marketStatus.isOpen;
            
            // Update market status display
            this.updateMarketStatusDisplay(marketStatus);
            
            // Adjust update frequency based on market status
            if (isOpen !== this.lastMarketStatus) {
                this.lastMarketStatus = isOpen;
                
                // Clear existing interval
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }
                
                if (isOpen) {
                    // Update every 30 seconds during market hours
                    console.log('Market is open - updating every 30 seconds');
                    this.updateInterval = setInterval(() => {
                        this.loadMarketData();
                    }, 30 * 1000);
                } else {
                    // Update every 6 hours when market is closed
                    console.log('Market is closed - updating every 6 hours');
                    this.updateInterval = setInterval(() => {
                        this.loadMarketData();
                    }, 6 * 3600 * 1000);
                }
            }
        };
        
        // Initial check
        updateMarketStatus();
        
        // Check market status every minute
        this.marketStatusInterval = setInterval(updateMarketStatus, 60 * 1000);
    }
    
    /**
     * Update the market status display in the UI
     * @param {object} marketStatus
     */
    updateMarketStatusDisplay(marketStatus) {
        // Find or create market status element
        let statusElement = document.getElementById('market-status');
        if (!statusElement) {
            // Create status element if it doesn't exist
            const container = document.querySelector('.market-overview') || document.querySelector('.market-banner');
            if (container) {
                statusElement = document.createElement('div');
                statusElement.id = 'market-status';
                statusElement.className = 'market-status';
                container.appendChild(statusElement);
            }
        }
        
        if (statusElement) {
            const statusClass = marketStatus.isOpen ? 'market-open' : 'market-closed';
            const statusText = marketStatus.isOpen ? 'Market Open' : 'Market Closed';
            const timeUntilText = this.formatTimeUntil(marketStatus.timeUntilNext);
            
            statusElement.className = `market-status ${statusClass}`;
            statusElement.innerHTML = `
                <div class="market-status-main">
                    <span class="market-status-indicator"></span>
                    <span class="market-status-text">${statusText}</span>
                </div>
                <div class="market-status-details">
                    <div class="market-time">${marketStatus.nyTime}</div>
                    <div class="market-next">${marketStatus.nextEvent} in ${timeUntilText}</div>
                </div>
            `;
        }
        
        // Add CSS styles if not already present
        this.addMarketStatusStyles();
    }
    
    /**
     * Add CSS styles for market status display
     */
    addMarketStatusStyles() {
        if (document.getElementById('market-status-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'market-status-styles';
        style.textContent = `
            .market-status {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 12px;
                margin: 8px 0;
                border-radius: 6px;
                font-size: 12px;
                border: 1px solid #e2e8f0;
            }
            
            .market-status.market-open {
                background-color: #f0fdf4;
                border-color: #16a34a;
                color: #166534;
            }
            
            .market-status.market-closed {
                background-color: #fef2f2;
                border-color: #dc2626;
                color: #991b1b;
            }
            
            .market-status-main {
                display: flex;
                align-items: center;
                gap: 6px;
            }
            
            .market-status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
            }
            
            .market-open .market-status-indicator {
                background-color: #16a34a;
                animation: pulse 2s infinite;
            }
            
            .market-closed .market-status-indicator {
                background-color: #dc2626;
            }
            
            .market-status-text {
                font-weight: 600;
            }
            
            .market-status-details {
                text-align: right;
                font-size: 11px;
                opacity: 0.8;
            }
            
            .market-time {
                margin-bottom: 2px;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            
            @media (max-width: 768px) {
                .market-status {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 4px;
                }
                
                .market-status-details {
                    text-align: left;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.marketOverview = new MarketOverview();
});

// Clean up on page unload
window.addEventListener('beforeunload', function() {
    if (window.marketOverview) {
        window.marketOverview.destroy();
    }
});
