/**
 * Common CSS Classes and Utilities
 * Shared styling patterns for the NewsMonitor application
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */

/* Layout Utilities */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.flex-column {
    display: flex;
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.gap-sm {
    gap: 8px;
}

.gap-md {
    gap: 16px;
}

.gap-lg {
    gap: 24px;
}

/* Spacing Utilities */
.m-0 { margin: 0; }
.mt-sm { margin-top: 8px; }
.mt-md { margin-top: 16px; }
.mt-lg { margin-top: 24px; }
.mb-sm { margin-bottom: 8px; }
.mb-md { margin-bottom: 16px; }
.mb-lg { margin-bottom: 24px; }

.p-0 { padding: 0; }
.p-sm { padding: 8px; }
.p-md { padding: 16px; }
.p-lg { padding: 24px; }

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-weight-normal { font-weight: 400; }
.font-weight-medium { font-weight: 500; }
.font-weight-semibold { font-weight: 600; }
.font-weight-bold { font-weight: 700; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

/* Color Utilities */
.text-primary { color: #3b82f6; }
.text-success { color: #10b981; }
.text-danger { color: #ef4444; }
.text-warning { color: #f59e0b; }
.text-muted { color: #6b7280; }
.text-dark { color: #1f2937; }

.bg-primary { background-color: #3b82f6; }
.bg-success { background-color: #10b981; }
.bg-danger { background-color: #ef4444; }
.bg-warning { background-color: #f59e0b; }
.bg-light { background-color: #f8fafc; }
.bg-white { background-color: #ffffff; }

/* ==========================================================================
   COMMON COMPONENTS
   ========================================================================== */

/* Card Components */
.card-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.card-modern:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.card-compact {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-header-modern {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 16px;
    font-weight: 700;
    font-size: 16px;
    color: #1e40af;
}

.card-header-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

/* Button Components */
.btn-modern {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #374151;
}

.btn-outline:hover {
    border-color: #3b82f6;
    color: #3b82f6;
}

/* Loading Components */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    min-height: 200px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 8px;
}

.loading-subtext {
    color: #9ca3af;
    font-size: 12px;
}

/* Error Components */
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    color: #991b1b;
}

.error-icon {
    font-size: 32px;
    margin-bottom: 16px;
    color: #dc2626;
}

.error-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 8px;
}

.error-message {
    font-size: 14px;
    text-align: center;
    line-height: 1.5;
}

/* Badge Components */
.badge-modern {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1;
}

.badge-primary {
    background: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: #047857;
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

/* Grid Components */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.grid-2-col {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.grid-3-col {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

/* ==========================================================================
   RESPONSIVE UTILITIES
   ========================================================================== */

@media (max-width: 768px) {
    .grid-2-col,
    .grid-3-col {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .card-modern {
        padding: 16px;
    }
    
    .gap-lg {
        gap: 16px;
    }
    
    .p-lg {
        padding: 16px;
    }
    
    .mt-lg,
    .mb-lg {
        margin-top: 16px;
        margin-bottom: 16px;
    }
}

@media (max-width: 576px) {
    .card-modern {
        padding: 12px;
        margin-left: -5px;
        margin-right: -5px;
    }
    
    .btn-modern {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .loading-container {
        padding: 30px 15px;
    }
}
