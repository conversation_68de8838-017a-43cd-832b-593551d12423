/**
 * Price Graph Component Styles
 * Dedicated styles for interactive price charts and related components
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */

/* ==========================================================================
   PRICE CHART CONTAINER
   ========================================================================== */

.price-chart-container {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.price-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
}

.price-chart-title {
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.price-chart-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* ==========================================================================
   TICKER SELECTOR
   ========================================================================== */

.ticker-selector-inline {
    position: relative;
}

.ticker-selector-inline select {
    appearance: none;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 8px 32px 8px 12px;
    font-weight: 500;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

.ticker-selector-inline select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.ticker-selector-inline select:hover {
    border-color: #9ca3af;
}

.ticker-selector-inline::after {
    content: '▼';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 12px;
    pointer-events: none;
}

/* ==========================================================================
   PRICE CHART DISPLAY
   ========================================================================== */

.price-chart-display {
    position: relative;
    min-height: 350px;
    background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
    border-radius: 8px;
    overflow: hidden;
}

.price-chart-canvas {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Real-time indicator */
.price-chart-live-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(255, 255, 255, 0.9);
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    color: #374151;
    backdrop-filter: blur(10px);
    z-index: 10;
}

.price-chart-live-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse-live 2s infinite;
}

@keyframes pulse-live {
    0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}

/* ==========================================================================
   PRICE SUMMARY COMPONENTS
   ========================================================================== */

.price-summary-overlay {
    position: absolute;
    top: 16px;
    left: 16px;
    background: rgba(255, 255, 255, 0.95);
    padding: 12px 16px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10;
}

.price-current {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
    line-height: 1;
}

.price-change {
    font-size: 14px;
    font-weight: 600;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.price-change.positive {
    color: #10b981;
}

.price-change.negative {
    color: #ef4444;
}

.price-change-icon {
    font-size: 12px;
}

/* ==========================================================================
   CHART LOADING STATES
   ========================================================================== */

.chart-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 20;
    backdrop-filter: blur(5px);
}

.chart-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.chart-loading-text {
    color: #6b7280;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.chart-loading-subtext {
    color: #9ca3af;
    font-size: 14px;
}

/* ==========================================================================
   CHART ERROR STATES
   ========================================================================== */

.chart-error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 20;
    border: 1px solid #fecaca;
    border-radius: 8px;
}

.chart-error-icon {
    font-size: 48px;
    color: #dc2626;
    margin-bottom: 16px;
}

.chart-error-title {
    font-size: 18px;
    font-weight: 600;
    color: #991b1b;
    margin-bottom: 8px;
}

.chart-error-message {
    font-size: 14px;
    color: #7f1d1d;
    text-align: center;
    line-height: 1.5;
    max-width: 300px;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 768px) {
    .price-chart-container {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .price-chart-header {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .price-chart-controls {
        justify-content: center;
    }
    
    .ticker-selector-inline select {
        width: 100%;
        min-width: auto;
    }
    
    .price-chart-display {
        min-height: 300px;
    }
    
    .price-summary-overlay {
        position: static;
        margin-bottom: 12px;
        background: rgba(248, 250, 252, 0.9);
    }
    
    .price-current {
        font-size: 20px;
    }
    
    .chart-loading-text {
        font-size: 14px;
    }
    
    .chart-error-title {
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .price-chart-container {
        padding: 12px;
        margin-left: -5px;
        margin-right: -5px;
    }
    
    .price-chart-display {
        min-height: 250px;
    }
    
    .price-current {
        font-size: 18px;
    }
    
    .price-change {
        font-size: 12px;
    }
    
    .chart-loading-spinner {
        width: 32px;
        height: 32px;
        border-width: 3px;
    }
    
    .chart-error-icon {
        font-size: 36px;
    }
}
