import logging
import time
from datetime import datetime, timedelta
from typing import Optional, Union

import pandas as pd
import yfinance as yf

from db.database import get_db_manager
from utils.logging_config import get_api_logger

logger = get_api_logger(__name__)


class YahooFinanceAPI:
    def __init__(
        self,
        max_retries: int = 3,
        retry_delay: int = 5
    ):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.db = get_db_manager().market_data_service
        logger.info("Initialized YahooFinanceAPI")

    def get_price_data(
        self,
        ticker: str,
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        period: Optional[str] = None,
        interval: str = "1d",
        force_refresh: bool = False
    ) -> pd.DataFrame:
        logger.info(f"Getting price data for {ticker}, interval={interval}, "
                    f"start={start_date}, end={end_date}, force_refresh={force_refresh}")
        if not ticker:
            logger.error("Ticker symbol is required")
            return pd.DataFrame()

        start_date = pd.to_datetime(start_date) if isinstance(
            start_date, str) else start_date
        end_date = pd.to_datetime(end_date) if isinstance(
            end_date, str) else end_date
        end_date = end_date or datetime.now()

        start_str = start_date.strftime('%Y-%m-%d') if start_date else None
        end_str = end_date.strftime('%Y-%m-%d')

        if not force_refresh:
            data = self._load_from_db_and_fetch_delta(
                ticker, start_str, end_str, period, interval)
            if not data.empty:
                logger.info(
                    f"Returning cached + delta data for {ticker} ({len(data)} rows)")
                return data

        logger.info(
            f"No cache or force refresh enabled — fetching full data for {ticker}")
        return self._fetch_all_data_to_db(ticker, start_date, end_date, period, interval)

    def _load_from_db_and_fetch_delta(
        self,
        ticker: str,
        start_date_str: Optional[str],
        end_date_str: str,
        period: Optional[str],
        interval: str
    ) -> pd.DataFrame:
        try:
            data = self.db.get_market_data(ticker, interval)

            if data.empty:
                logger.info(
                    f"No cached data for {ticker}, fetching full range")
                return self._fetch_all_data_to_db(
                    ticker,
                    start_date=pd.to_datetime(
                        start_date_str) if start_date_str else None,
                    end_date=pd.to_datetime(end_date_str),
                    period=period,
                    interval=interval
                )

            logger.info(
                f"Loaded {len(data)} rows from DB for {ticker} ({interval})")

            first_date = data.index[0]
            last_date = data.index[-1]
            end_date = pd.to_datetime(end_date_str)
            start_date = pd.to_datetime(
                start_date_str) if start_date_str else None

            # Fetch historical delta
            if start_date and start_date < first_date:
                logger.info(
                    f"Fetching historical delta from {start_date.date()} to {first_date.date()}")
                historical_data = self._fetch_data(
                    ticker, start_date, first_date - pd.Timedelta(days=1), None, interval)
                if not historical_data.empty:
                    self.db.upsert_market_data(
                        ticker, interval, historical_data)

            # Fetch future delta
            if last_date <= end_date:
                logger.info(
                    f"Fetching future delta from {last_date.date()} to {end_date.date()}")
                future_data = self._fetch_data(
                    ticker, last_date, end_date, None, interval)
                if not future_data.empty:
                    self.db.upsert_market_data(ticker, interval, future_data)

            # Reload data for the full desired range
            combined = self.db.get_market_data(
                ticker, interval, start_date_str, end_date_str)
            logger.info(f"Returning {len(combined)} rows after delta update")
            return combined

        except Exception as e:
            logger.error(f"Error in _load_from_db_and_fetch_delta: {e}")
            return pd.DataFrame()

    def _fetch_all_data_to_db(
        self,
        ticker: str,
        start_date: Optional[datetime],
        end_date: datetime,
        period: Optional[str],
        interval: str
    ) -> pd.DataFrame:
        logger.info(
            f"Fetching full dataset for {ticker}, start={start_date}, end={end_date}")
        df = self._fetch_data(ticker, start_date, end_date, period, interval)

        if not df.empty:
            logger.info(f"Fetched {len(df)} rows for {ticker}, saving to DB")
            self.db.upsert_market_data(ticker, interval, df)
        else:
            logger.warning(f"No data fetched for {ticker} from Yahoo")

        return df

    def _fetch_data(
        self,
        ticker: str,
        start_date: Optional[datetime],
        end_date: datetime,
        period: Optional[str],
        interval: str
    ) -> pd.DataFrame:
        # Add one day to end_date to include the end_date in the range
        end_str = (end_date + timedelta(days=1)).strftime('%Y-%m-%d')
        start_str = start_date.strftime('%Y-%m-%d') if start_date else None

        for attempt in range(1, self.max_retries + 1):
            try:
                if start_date is None and period:
                    logger.info(
                        f"Downloading data for {ticker} using period={period}")
                    df = yf.download(
                        ticker, period=period, end=end_str, interval=interval, progress=False)
                else:
                    logger.info(
                        f"Downloading data for {ticker} from {start_str} to {end_str}")
                    df = yf.download(
                        ticker, start=start_str, end=end_str, interval=interval, progress=False)

                if df.empty:
                    logger.warning(f"No data received from Yahoo for {ticker}")
                    return pd.DataFrame()

                return self._sanitize_yfinance_df(df, ticker)

            except Exception as e:
                logger.warning(
                    f"[{attempt}/{self.max_retries}] Error fetching data for {ticker}: {e}")
                if attempt < self.max_retries:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(
                        f"Max retries reached for {ticker}. Failing fetch.")
                    return pd.DataFrame()

    def _sanitize_yfinance_df(self, df: pd.DataFrame, ticker: str) -> pd.DataFrame:
        try:
            logger.debug(f"Sanitizing Yahoo Finance dataframe for {ticker}")

            if isinstance(df.columns, pd.MultiIndex):
                for i, level_values in enumerate(df.columns.levels):
                    if ticker in level_values:
                        df.columns = df.columns.droplevel(i)
                        logger.debug(
                            f"Dropped multiindex level {i} containing {ticker}")
                        break

            df.columns = [c.capitalize() for c in df.columns]
            df = df[['Open', 'High', 'Low', 'Close', 'Volume']].copy()
            df.index = pd.to_datetime(df.index)

            for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            logger.debug(f"Sanitization complete for {ticker}")
            return df

        except Exception as e:
            logger.error(f"Failed to sanitize data for {ticker}: {e}")
            return pd.DataFrame()


yahoo_api = YahooFinanceAPI()


def main():
    yahoo_api.get_price_data(
        ticker='SPY',
        interval='1d',
        period='5y',
        force_refresh=True
    )


if __name__ == "__main__":
    main()
