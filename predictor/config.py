"""
Configuration settings for the SP500 price predictor.
"""

import os
from pathlib import Path

# Project paths
ROOT_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
DATA_DIR = ROOT_DIR / "data"
OUTPUT_DIR = ROOT_DIR / "output"
MODELS_DIR = OUTPUT_DIR / "models"
LOGS_DIR = ROOT_DIR / "logs"
DATASETS_DIR = OUTPUT_DIR / "datasets"

# Create directories if they don't exist
for directory in [DATA_DIR, MODELS_DIR, OUTPUT_DIR, LOGS_DIR, DATASETS_DIR]:
    directory.mkdir(exist_ok=True, parents=True)

# News data settings
ARTICLE_FEATURE_DAYS = 7
PRICE_FEATURE_DAYS = 30
MAX_ARTICLES_PER_DAY = 50

# SP500 data settings
SP500_TICKER = "SPY"

# Model settings
MODEL_DIR = Path(os.path.dirname(ROOT_DIR)) / "predictor" / "output" / "models"
BASE_MODEL_NAME = "ProsusAI/finbert"
MODEL_MAX_LENGTH = 512
INDICATOR_MODEL_NAME = "indicator_model_v0"
PREDICTION_MODEL_NAME = "prediction_model_v0"
FINE_TUNED_LAYERS = 2

# Training settings - parameters for BERT fine-tuning
TRAIN_TEST_SPLIT = 0.2
VALIDATION_SPLIT = 0.1
BATCH_SIZE = 4
LEARNING_RATE = 2e-5
NUM_EPOCHS = 5
WARMUP_RATIO = 0.1
WEIGHT_DECAY = 0.01
LOGGING_STEPS = 10

# Classification settings
CLASS_LABELS = {
    0: "positive",  # >threshold% return
    1: "negative",  # <-threshold% loss
    2: "neutral"    # Between -threshold% and threshold%
}
THRESHOLD = 1.0

# Data augmentation settings
USE_DATA_AUGMENTATION = True
AUGMENTATION_TECHNIQUES = ["chunk"]
# Chunk augmentation settings
CHUNK_MIN_SIZE_WORDS = 20
# Minimum number of words for a chunk to be considered valid
# Whether to preserve the original article's label for all chunks
PRESERVE_LABEL_FOR_CHUNKS = True

# Feature extraction settings
FEATURE_EXTRACTION_CONFIG = {
    # News feature extractor settings
    'news_config': {
        'lookback_periods': [1, 3],      # Lookback periods in days
        'days_back': ARTICLE_FEATURE_DAYS,
        'include_statistics': True,        # Whether to include statistical features
        'statistics': ['median', 'std'],
        'use_article_counts': False,        # Whether to include article counts
        'use_sentiment': False,             # Whether to include sentiment features
        # Whether to use indicator model for classification
        'use_indicator_model': True,
        'use_percentages': True,
        'use_raw_counts': False
    },
    # Price feature extractor settings
    'price_config': {
        'lookback_periods': [1, 3, 7, 30],  # Lookback periods in days
        'days_back': PRICE_FEATURE_DAYS,
        'use_price_indicators': True,       # Whether to use technical indicators
        # Technical indicators to use
        'indicators': ['ema', 'rsi', 'macd'],
        # Whether to return price sequence features
        'use_price_sequence': False,
        'use_return_features': True
    }
}

# Prediction dataset settings
PREDICTION_DATASET_CONFIG = {
    'news_days': ARTICLE_FEATURE_DAYS,
    'price_days': PRICE_FEATURE_DAYS,
    'ticker': SP500_TICKER,
    'threshold': THRESHOLD,
    'feature_config': FEATURE_EXTRACTION_CONFIG,
    'use_augmentation': False,
    'augmentation_techniques': ""
}


# Date format settings
DATE_FORMAT = "%Y-%m-%d"
TZ_NAME = "America/New_York"

# Logging settings
TENSORBOARD_LOG_DIR = LOGS_DIR / "tensorboard"
TENSORBOARD_LOG_DIR.mkdir(exist_ok=True, parents=True)

# Llm predictor settings
MAX_ARTICLES_TO_ANALYZE = 30
MAX_WORDS_PER_ARTICLE = 300
