"""
LLM-based model for SP500 price prediction.

This model uses Large Language Models to predict market direction based on
news sentiment and technical indicators.
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import pandas as pd

from predictor.models.base_model import BasePredictionModel
from predictor.llm.llm_predictor import LLMPredictor
from predictor.config import CLASS_LABELS
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class LLMModel(BasePredictionModel):
    """LLM-based prediction model for market direction."""

    def __init__(self, model_config: Dict[str, Any]):
        """
        Initialize the LLM model.

        Args:
            model_config: Model configuration dictionary
        """
        super().__init__(model_config)

        # LLM-specific configuration
        self.api_configs = model_config.get('api_configs', {})
        self.preferred_api = model_config.get('preferred_api', 'gemini')
        self.max_articles = model_config.get('max_articles', 15)
        self.force_update = model_config.get('force_update', False)
        self.cache_duration_hours = model_config.get('cache_duration_hours', 1)

        # Initialize LLM predictor
        self.llm_predictor = None
        self._initialize_predictor()

    def _initialize_predictor(self):
        """Initialize the LLM predictor."""
        try:
            self.llm_predictor = LLMPredictor(
                api_configs=self.api_configs,
                force_update=self.force_update,
                cache_duration_hours=self.cache_duration_hours
            )
            logger.info("LLM predictor initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize LLM predictor: {e}")
            self.llm_predictor = None

    def build_model(self) -> Any:
        """
        Build the LLM model (no training required).

        Returns:
            The LLM predictor instance
        """
        if self.llm_predictor is None:
            self._initialize_predictor()

        self.model = self.llm_predictor
        return self.model

    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Train the LLM model (no training required for LLM).

        Args:
            X_train: Training features (not used for LLM)
            y_train: Training labels (not used for LLM)
            X_val: Validation features (not used for LLM)
            y_val: Validation labels (not used for LLM)
            output_dir: Output directory for saving model

        Returns:
            Empty metrics dictionary (no training metrics for LLM)
        """
        logger.info("LLM model does not require training")

        # Build the model
        self.build_model()

        # Save the model if output directory is provided
        if output_dir:
            self.save(output_dir)

        return {}

    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Make predictions using the LLM model.

        Args:
            X: Input features (should contain articles and market data)

        Returns:
            Predicted class labels
        """
        predictions, _ = self.predict_with_confidence(X)
        return predictions

    def predict_with_confidence(
        self,
        X: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions with confidence scores.

        Args:
            X: Input features (should be structured data with articles and market info)

        Returns:
            Tuple of (predictions, probabilities)
        """
        if self.llm_predictor is None:
            logger.error("LLM predictor not initialized")
            return self._create_fallback_predictions(len(X))

        predictions = []
        probabilities = []

        for sample in X:
            try:
                # Extract data from sample
                articles, current_price, price_data, vix_level = self._extract_sample_data(
                    sample)

                # Get LLM prediction
                result = asyncio.run(
                    self.llm_predictor.predict_market_direction(
                        articles=articles,
                        current_price=current_price,
                        price_data=price_data,
                        vix_level=vix_level,
                        preferred_api=self.preferred_api,
                        max_articles=self.max_articles
                    )
                )

                # Convert to class prediction
                prediction_class = self._convert_prediction_to_class(
                    result['prediction'])
                predictions.append(prediction_class)

                # Extract probabilities
                prob_array = self._convert_probabilities_to_array(
                    result['probabilities'])
                probabilities.append(prob_array)

            except Exception as e:
                logger.error(f"Error making LLM prediction: {e}")
                # Fallback prediction
                predictions.append(2)  # Neutral class
                probabilities.append([0.33, 0.33, 0.34])

        return np.array(predictions), np.array(probabilities)

    def predict_single(
        self,
        articles: List[Dict[str, Any]],
        current_price: float,
        price_data: Optional[pd.DataFrame] = None,
        vix_level: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Make a single prediction with detailed results.

        Args:
            articles: List of news articles with sentiment
            current_price: Current SP500 price
            price_data: Historical price data
            vix_level: Current VIX level

        Returns:
            Detailed prediction result
        """
        if self.llm_predictor is None:
            logger.error("LLM predictor not initialized")
            return self._create_fallback_result()

        try:
            result = asyncio.run(
                self.llm_predictor.predict_market_direction(
                    articles=articles,
                    current_price=current_price,
                    price_data=price_data,
                    vix_level=vix_level,
                    preferred_api=self.preferred_api,
                    max_articles=self.max_articles
                )
            )

            # Add class prediction
            result['prediction_class'] = self._convert_prediction_to_class(
                result['prediction'])

            return result

        except Exception as e:
            logger.error(f"Error making single LLM prediction: {e}")
            return self._create_fallback_result()

    def _extract_sample_data(self, sample) -> Tuple[List[Dict], float, Optional[pd.DataFrame], Optional[float]]:
        """Extract data from a sample for prediction."""
        # This is a simplified extraction - in practice, you'd need to define
        # how the input data is structured
        if isinstance(sample, dict):
            articles = sample.get('articles', [])
            current_price = sample.get('current_price', 500.0)
            price_data = sample.get('price_data')
            vix_level = sample.get('vix_level')
        else:
            # Fallback for array input
            articles = []
            current_price = 500.0
            price_data = None
            vix_level = None

        return articles, current_price, price_data, vix_level

    def _convert_prediction_to_class(self, prediction: str) -> int:
        """Convert string prediction to class index."""
        prediction_map = {
            'up': 0,
            'positive': 0,
            'down': 1,
            'negative': 1,
            'flat': 2,
            'neutral': 2,
            'sideways': 2
        }
        return prediction_map.get(prediction.lower(), 2)  # Default to neutral

    def _convert_probabilities_to_array(self, probabilities: Dict[str, float]) -> List[float]:
        """Convert probability dictionary to array."""
        return [
            probabilities.get('positive', 0.33),
            probabilities.get('negative', 0.33),
            probabilities.get('neutral', 0.34)
        ]

    def _create_fallback_predictions(self, num_samples: int) -> Tuple[np.ndarray, np.ndarray]:
        """Create fallback predictions when LLM fails."""
        predictions = np.full(num_samples, 2)  # All neutral
        probabilities = np.array([[0.33, 0.33, 0.34]] * num_samples)
        return predictions, probabilities

    def _create_fallback_result(self) -> Dict[str, Any]:
        """Create fallback result when LLM fails."""
        return {
            "prediction": "neutral",
            "prediction_class": 2,
            "confidence": 0.33,
            "probabilities": {"positive": 0.33, "negative": 0.33, "neutral": 0.34},
            "key_evidence": ["LLM prediction unavailable"],
            "detailed_outlook": {},
            "dominant_theme": "Unable to analyze market conditions",
            "critical_levels": {},
            "metadata": {
                "api": "fallback",
                "model": "none",
                "error": "LLM predictor not available"
            }
        }

    def _save_model_specific(self, path: str) -> None:
        """Save LLM model specific data."""
        # Save LLM configuration
        llm_config = {
            'api_configs': self.api_configs,
            'preferred_api': self.preferred_api,
            'max_articles': self.max_articles,
            'force_update': self.force_update,
            'cache_duration_hours': self.cache_duration_hours
        }

        llm_config_path = os.path.join(path, "llm_config.json")
        with open(llm_config_path, 'w') as f:
            json.dump(llm_config, f, indent=4)

        logger.info(f"LLM model configuration saved to {llm_config_path}")

    def _load_model_specific(self, path: str) -> None:
        """Load LLM model specific data."""
        llm_config_path = os.path.join(path, "llm_config.json")

        if os.path.exists(llm_config_path):
            with open(llm_config_path, 'r') as f:
                llm_config = json.load(f)

            self.api_configs = llm_config.get('api_configs', {})
            self.preferred_api = llm_config.get('preferred_api', 'gemini')
            self.max_articles = llm_config.get('max_articles', 15)
            self.force_update = llm_config.get('force_update', False)
            self.cache_duration_hours = llm_config.get(
                'cache_duration_hours', 1)

            # Reinitialize predictor with loaded config
            self._initialize_predictor()

            logger.info(
                f"LLM model configuration loaded from {llm_config_path}")
        else:
            logger.warning(f"LLM config file not found at {llm_config_path}")

    def get_supported_apis(self) -> List[str]:
        """Get list of supported APIs."""
        if self.llm_predictor:
            return self.llm_predictor.get_supported_apis()
        return []

    async def test_api_connections(self) -> Dict[str, bool]:
        """Test connections to all configured APIs."""
        if not self.llm_predictor:
            return {}

        results = {}
        for api_name in self.llm_predictor.get_supported_apis():
            results[api_name] = await self.llm_predictor.test_api_connection(api_name)

        return results
