"""
Gemini Fine-Tuning Manager

This module extends the Gemini API manager to support fine-tuning operations
using the google-genai SDK with proper batch processing and job management.
"""

import json
import os
import time
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from pathlib import Path

from google import genai
from google.genai.types import CreateBatchJobConfig, JobState, HttpOptions
from google.cloud import storage

from apis.llm.gemini import GeminiManager
from db.database import get_db_manager
from predictor.fine_tuning.config import (
    MODEL_CONFIG, 
    GCS_CONFIG, 
    API_CONFIG,
    TRAINING_DATA_TEMPLATES
)
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class GeminiFineTuner(GeminiManager):
    """Extended Gemini manager with fine-tuning capabilities."""
    
    def __init__(self, **kwargs):
        # Initialize with fine-tuning specific configuration
        fine_tuning_config = API_CONFIG.get('gemini', {})
        
        # Override bucket name for fine-tuning
        kwargs.setdefault('bucket_name', GCS_CONFIG['bucket_name'])
        
        super().__init__(**kwargs)
        
        self.db = get_db_manager()
        self.project_id = fine_tuning_config.get('project_id') or os.getenv("GOOGLE_CLOUD_PROJECT")
        self.location = fine_tuning_config.get('location') or os.getenv("GOOGLE_CLOUD_LOCATION")
        
        if not self.project_id or not self.location:
            raise ValueError("Missing Google Cloud project configuration for fine-tuning")
    
    def upload_training_data(self, training_samples: List[Dict[str, Any]], 
                           dataset_name: str = None) -> str:
        """
        Upload training data to Google Cloud Storage in the format required for fine-tuning.
        
        Args:
            training_samples: List of training samples with messages format
            dataset_name: Optional name for the dataset
            
        Returns:
            GCS URI of the uploaded training data
        """
        if not self.storage_client or not self.bucket_name:
            raise ValueError("Google Cloud Storage not configured for fine-tuning")
        
        # Generate dataset name if not provided
        if not dataset_name:
            timestamp = int(time.time())
            dataset_name = f"training_data_{timestamp}_{uuid.uuid4().hex[:8]}"
        
        # Create JSONL content for fine-tuning
        jsonl_lines = []
        for sample in training_samples:
            # Ensure proper format for fine-tuning
            if 'messages' not in sample:
                logger.warning("Training sample missing 'messages' field, skipping")
                continue
            
            # Convert to fine-tuning format
            fine_tuning_sample = {
                'messages': sample['messages']
            }
            
            # Add metadata if present
            if 'metadata' in sample:
                fine_tuning_sample['metadata'] = sample['metadata']
            
            jsonl_lines.append(json.dumps(fine_tuning_sample))
        
        if not jsonl_lines:
            raise ValueError("No valid training samples found")
        
        # Upload to GCS
        try:
            filename = f"{GCS_CONFIG['training_data_prefix']}/{dataset_name}.jsonl"
            bucket = self.storage_client.bucket(self.bucket_name)
            blob = bucket.blob(filename)
            
            content = '\n'.join(jsonl_lines)
            blob.upload_from_string(content, content_type='application/jsonl')
            
            gcs_uri = f"gs://{self.bucket_name}/{filename}"
            logger.info(f"Uploaded training data to {gcs_uri} ({len(jsonl_lines)} samples)")
            
            return gcs_uri
            
        except Exception as e:
            logger.error(f"Failed to upload training data: {e}")
            raise
    
    def create_fine_tuning_job(self, 
                              training_data_uri: str,
                              base_model: str = None,
                              job_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a fine-tuning job using the Gemini API.
        
        Args:
            training_data_uri: GCS URI of the training data
            base_model: Base model to fine-tune
            job_config: Additional job configuration
            
        Returns:
            Fine-tuning job information
        """
        base_model = base_model or MODEL_CONFIG['base_model']
        job_config = job_config or {}
        
        try:
            # Generate unique job name
            timestamp = int(time.time())
            job_name = f"fine_tune_{timestamp}_{uuid.uuid4().hex[:8]}"
            
            # Prepare fine-tuning configuration
            fine_tuning_config = {
                'learning_rate': job_config.get('learning_rate', MODEL_CONFIG['learning_rate']),
                'batch_size': job_config.get('batch_size', MODEL_CONFIG['batch_size']),
                'num_epochs': job_config.get('num_epochs', MODEL_CONFIG['num_epochs']),
            }
            
            # Create fine-tuning job using the Gemini API
            # Note: This is a placeholder for the actual fine-tuning API call
            # The exact API may vary based on Google's implementation
            
            job_data = {
                'id': job_name,
                'base_model': base_model,
                'training_data_uri': training_data_uri,
                'config': fine_tuning_config,
                'status': 'pending',
                'created_at': datetime.now(timezone.utc),
                'project_id': self.project_id,
                'location': self.location
            }
            
            logger.info(f"Created fine-tuning job: {job_name}")
            return job_data
            
        except Exception as e:
            logger.error(f"Failed to create fine-tuning job: {e}")
            raise
    
    def get_fine_tuning_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get the status of a fine-tuning job.
        
        Args:
            job_id: ID of the fine-tuning job
            
        Returns:
            Job status information
        """
        try:
            # This would use the actual Gemini fine-tuning API
            # For now, we'll return a placeholder status
            
            job_status = {
                'id': job_id,
                'status': 'running',  # pending, running, completed, failed
                'progress': 0.5,
                'metrics': {},
                'error_message': None
            }
            
            return job_status
            
        except Exception as e:
            logger.error(f"Failed to get fine-tuning job status: {e}")
            raise
    
    def list_fine_tuning_jobs(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        List fine-tuning jobs.
        
        Args:
            limit: Maximum number of jobs to return
            
        Returns:
            List of fine-tuning job information
        """
        try:
            # This would use the actual Gemini fine-tuning API
            # For now, we'll return jobs from our database
            
            jobs = self.db.fine_tuning_service.get_fine_tuning_jobs(
                api='gemini',
                limit=limit
            )
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to list fine-tuning jobs: {e}")
            return []
    
    def cancel_fine_tuning_job(self, job_id: str) -> bool:
        """
        Cancel a fine-tuning job.
        
        Args:
            job_id: ID of the job to cancel
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # This would use the actual Gemini fine-tuning API
            # For now, we'll update our database
            
            success = self.db.fine_tuning_service.update_fine_tuning_job(
                job_id, {'status': 'cancelled'}
            )
            
            if success:
                logger.info(f"Cancelled fine-tuning job: {job_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to cancel fine-tuning job: {e}")
            return False
    
    def download_fine_tuned_model(self, job_id: str, output_dir: str) -> Optional[str]:
        """
        Download a fine-tuned model.
        
        Args:
            job_id: ID of the completed fine-tuning job
            output_dir: Directory to save the model
            
        Returns:
            Path to the downloaded model or None if failed
        """
        try:
            # Get job information
            job = self.db.fine_tuning_service.get_fine_tuning_job(job_id)
            if not job or job['status'] != 'completed':
                logger.error(f"Job {job_id} is not completed or not found")
                return None
            
            # Create output directory
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # This would download the actual model from Google Cloud
            # For now, we'll create a placeholder
            
            model_path = output_path / f"fine_tuned_model_{job_id}"
            model_path.mkdir(exist_ok=True)
            
            # Save model metadata
            metadata = {
                'job_id': job_id,
                'base_model': job.get('model_name'),
                'fine_tuned_model_name': job.get('fine_tuned_model_name'),
                'training_config': job.get('training_config'),
                'final_metrics': job.get('final_metrics'),
                'downloaded_at': datetime.now(timezone.utc).isoformat()
            }
            
            metadata_file = model_path / 'metadata.json'
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            logger.info(f"Downloaded fine-tuned model to {model_path}")
            return str(model_path)
            
        except Exception as e:
            logger.error(f"Failed to download fine-tuned model: {e}")
            return None
    
    def validate_training_data_format(self, training_samples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate training data format for fine-tuning.
        
        Args:
            training_samples: List of training samples
            
        Returns:
            Validation results
        """
        validation_results = {
            'is_valid': True,
            'total_samples': len(training_samples),
            'valid_samples': 0,
            'errors': [],
            'warnings': []
        }
        
        for i, sample in enumerate(training_samples):
            sample_errors = []
            
            # Check required fields
            if 'messages' not in sample:
                sample_errors.append(f"Sample {i}: Missing 'messages' field")
            else:
                messages = sample['messages']
                if not isinstance(messages, list) or len(messages) < 2:
                    sample_errors.append(f"Sample {i}: 'messages' must be a list with at least 2 messages")
                else:
                    # Check message format
                    for j, message in enumerate(messages):
                        if not isinstance(message, dict):
                            sample_errors.append(f"Sample {i}, message {j}: Must be a dictionary")
                            continue
                        
                        if 'role' not in message or 'content' not in message:
                            sample_errors.append(f"Sample {i}, message {j}: Missing 'role' or 'content'")
                        
                        if message.get('role') not in ['system', 'user', 'assistant']:
                            sample_errors.append(f"Sample {i}, message {j}: Invalid role '{message.get('role')}'")
            
            if sample_errors:
                validation_results['errors'].extend(sample_errors)
                validation_results['is_valid'] = False
            else:
                validation_results['valid_samples'] += 1
        
        return validation_results
