"""
Tests for the Fine-Tuning Pipeline

This module contains comprehensive tests for the fine-tuning infrastructure
including data pipeline, price validation, and training operations.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json

from predictor.fine_tuning.data_pipeline import (
    <PERSON><PERSON><PERSON>E<PERSON>tractor,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
    TrainingDataPipeline
)
from predictor.fine_tuning.price_validator import (
    PriceMovementCalculator,
    PriceValidator,
    PriceValidationFilter
)
from predictor.fine_tuning.training_pipeline import FineTuningTrainingPipeline
from predictor.fine_tuning.fine_tuned_indicator import FineTunedIndicatorModel


class TestTrainingDataExtractor(unittest.TestCase):
    """Test the training data extraction functionality."""
    
    def setUp(self):
        self.mock_db = Mock()
        self.extractor = TrainingDataExtractor(self.mock_db)
    
    def test_validate_influence_score(self):
        """Test influence score validation."""
        # Valid scores
        self.assertTrue(self.extractor.validate_influence_score(0))
        self.assertTrue(self.extractor.validate_influence_score(-5))
        self.assertTrue(self.extractor.validate_influence_score(5))
        self.assertTrue(self.extractor.validate_influence_score(3))
        
        # Invalid scores
        self.assertFalse(self.extractor.validate_influence_score(-6))
        self.assertFalse(self.extractor.validate_influence_score(6))
        self.assertFalse(self.extractor.validate_influence_score("invalid"))
        self.assertFalse(self.extractor.validate_influence_score(None))
    
    def test_validate_article_content(self):
        """Test article content validation."""
        # Valid article
        valid_article = {
            'title': 'Test Article',
            'content': 'This is a test article with sufficient content for validation purposes.',
            'date': '2024-01-01',
            'article_metadata': {
                'influence_tagging': {
                    'influence': 2
                }
            }
        }
        self.assertTrue(self.extractor.validate_article_content(valid_article))
        
        # Missing required fields
        invalid_article = {
            'title': 'Test Article'
            # Missing content and date
        }
        self.assertFalse(self.extractor.validate_article_content(invalid_article))
        
        # Missing influence tagging
        no_influence_article = {
            'title': 'Test Article',
            'content': 'Test content',
            'date': '2024-01-01',
            'article_metadata': {}
        }
        self.assertFalse(self.extractor.validate_article_content(no_influence_article))
    
    def test_extract_influence_data(self):
        """Test influence data extraction."""
        article = {
            'id': 'test_article',
            'article_metadata': {
                'influence_tagging': {
                    'influence': 3,
                    'category': 'earnings',
                    'tags': ['positive', 'growth'],
                    'regions': ['US'],
                    'reason': 'Strong earnings report'
                }
            }
        }
        
        result = self.extractor.extract_influence_data(article)
        
        self.assertIsNotNone(result)
        self.assertEqual(result['influence_score'], 3)
        self.assertEqual(result['category'], 'earnings')
        self.assertEqual(result['tags'], ['positive', 'growth'])
        self.assertEqual(result['regions'], ['US'])
        self.assertEqual(result['reason'], 'Strong earnings report')


class TestTrainingDataFormatter(unittest.TestCase):
    """Test the training data formatting functionality."""
    
    def setUp(self):
        self.formatter = TrainingDataFormatter('classification')
    
    def test_format_training_sample(self):
        """Test training sample formatting."""
        article = {
            'id': 'test_article',
            'title': 'Test Article Title',
            'content': 'This is test article content.',
            'source': 'Test Source',
            'date': '2024-01-01'
        }
        
        influence_data = {
            'influence_score': 2,
            'category': 'earnings',
            'tags': ['positive'],
            'regions': ['US']
        }
        
        result = self.formatter.format_training_sample(article, influence_data)
        
        # Check structure
        self.assertIn('messages', result)
        self.assertIn('metadata', result)
        
        # Check messages format
        messages = result['messages']
        self.assertEqual(len(messages), 3)  # system, user, assistant
        self.assertEqual(messages[0]['role'], 'system')
        self.assertEqual(messages[1]['role'], 'user')
        self.assertEqual(messages[2]['role'], 'assistant')
        
        # Check metadata
        metadata = result['metadata']
        self.assertEqual(metadata['article_id'], 'test_article')
        self.assertEqual(metadata['influence_score'], 2)
        self.assertEqual(metadata['source'], 'Test Source')


class TestPriceValidator(unittest.TestCase):
    """Test the price validation functionality."""
    
    def setUp(self):
        self.mock_db = Mock()
        self.calculator = PriceMovementCalculator(self.mock_db)
        self.validator = PriceValidator()
    
    @patch('predictor.fine_tuning.price_validator.get_db_manager')
    def test_calculate_price_change(self, mock_get_db):
        """Test price change calculation."""
        # Mock price data
        mock_price_data = Mock()
        mock_price_data.empty = False
        mock_price_data.index = [
            datetime(2024, 1, 1),
            datetime(2024, 1, 2),
            datetime(2024, 1, 3)
        ]
        mock_price_data.loc = {
            datetime(2024, 1, 1): Mock(close_price=100.0),
            datetime(2024, 1, 3): Mock(close_price=105.0)
        }
        
        mock_get_db.return_value = self.mock_db
        self.mock_db.market_data_service.get_market_data.return_value = mock_price_data
        
        # Test calculation
        article_date = datetime(2024, 1, 1)
        result = self.calculator.calculate_price_change(article_date, 2)
        
        # Should calculate 5% increase
        self.assertIsNotNone(result)
    
    def test_validate_sample(self):
        """Test sample validation."""
        training_sample = {
            'metadata': {
                'influence_score': 2,
                'date': '2024-01-01'
            }
        }
        
        # Mock the price calculator
        with patch.object(self.validator.calculator, 'calculate_price_change', return_value=3.0):
            result = self.validator.validate_sample(training_sample, 3)
            
            self.assertIsNotNone(result)
            self.assertEqual(result.validation_days, 3)
            self.assertEqual(result.price_change_percent, 3.0)
            self.assertTrue(result.direction_match)  # Both positive


class TestFineTuningTrainingPipeline(unittest.TestCase):
    """Test the complete training pipeline."""
    
    def setUp(self):
        self.pipeline = FineTuningTrainingPipeline()
    
    @patch('predictor.fine_tuning.training_pipeline.get_db_manager')
    @patch('predictor.fine_tuning.training_pipeline.TrainingDataPipeline')
    @patch('predictor.fine_tuning.training_pipeline.PriceValidationFilter')
    @patch('predictor.fine_tuning.training_pipeline.GeminiFineTuner')
    def test_prepare_training_data(self, mock_fine_tuner, mock_price_filter, mock_data_pipeline, mock_get_db):
        """Test training data preparation."""
        # Mock data pipeline
        mock_data_pipeline_instance = Mock()
        mock_data_pipeline.return_value = mock_data_pipeline_instance
        mock_data_pipeline_instance.prepare_training_data.return_value = (
            [{'sample': 'data'}], {'stats': 'data'}
        )
        
        # Mock price filter
        mock_price_filter_instance = Mock()
        mock_price_filter.return_value = mock_price_filter_instance
        mock_price_filter_instance.filter_training_samples.return_value = (
            [{'sample': 'data'}], {'validation': 'stats'}
        )
        
        # Mock fine tuner
        mock_fine_tuner_instance = Mock()
        mock_fine_tuner.return_value = mock_fine_tuner_instance
        mock_fine_tuner_instance.validate_training_data_format.return_value = {
            'is_valid': True,
            'errors': []
        }
        
        # Test preparation
        samples, stats = self.pipeline.prepare_training_data(
            max_samples=100,
            enable_price_validation=True
        )
        
        self.assertEqual(len(samples), 1)
        self.assertIn('stats', stats)
        self.assertIn('price_validation', stats)


class TestFineTunedIndicatorModel(unittest.TestCase):
    """Test the fine-tuned indicator model."""
    
    def setUp(self):
        self.model = FineTunedIndicatorModel()
    
    @patch('predictor.fine_tuning.fine_tuned_indicator.get_db_manager')
    def test_load_from_job(self, mock_get_db):
        """Test loading model from job."""
        # Mock database
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock job data
        mock_job = {
            'id': 'test_job_id',
            'status': 'completed',
            'fine_tuned_model_name': 'test_model',
            'model_name': 'base_model',
            'training_config': {},
            'data_config': {},
            'final_metrics': {},
            'training_samples_count': 100,
            'created_at': datetime.now(),
            'completed_at': datetime.now()
        }
        
        mock_db.fine_tuning_service.get_fine_tuning_job.return_value = mock_job
        
        # Test loading
        success = self.model.load_from_job('test_job_id')
        
        self.assertTrue(success)
        self.assertTrue(self.model.is_loaded)
        self.assertEqual(self.model.job_id, 'test_job_id')
        self.assertEqual(self.model.model_name, 'test_model')
    
    def test_predict_single(self):
        """Test single article prediction."""
        # Mock model as loaded
        self.model.is_loaded = True
        self.model.model_name = 'test_model'
        self.model.job_id = 'test_job'
        self.model.model_metadata = {'data_config': {'output_format': 'classification'}}
        
        # Test prediction
        result = self.model.predict_single(
            "This is a positive financial news article about growth.",
            {'title': 'Test Article', 'source': 'Test Source'}
        )
        
        self.assertIn('prediction', result)
        self.assertIn('confidence', result)
        self.assertIn('model_name', result)
        self.assertEqual(result['model_name'], 'test_model')
        self.assertEqual(result['model_type'], 'fine_tuned_llm')


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete fine-tuning system."""
    
    @patch('predictor.fine_tuning.training_pipeline.get_db_manager')
    def test_end_to_end_pipeline(self, mock_get_db):
        """Test end-to-end pipeline execution."""
        # This would be a more comprehensive integration test
        # For now, just test that components can be instantiated together
        
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Test component instantiation
        pipeline = FineTuningTrainingPipeline()
        model = FineTunedIndicatorModel()
        
        self.assertIsNotNone(pipeline)
        self.assertIsNotNone(model)
        
        # Test that they use the same database manager
        self.assertEqual(pipeline.db, mock_db)


if __name__ == '__main__':
    unittest.main()
