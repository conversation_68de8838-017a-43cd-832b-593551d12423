"""
Command Line Interface for Fine-Tuning Operations

This module provides a comprehensive CLI for managing LLM fine-tuning operations
including training, monitoring, and model management.
"""

import argparse
import json
import sys
from datetime import datetime, timedelta
from typing import Optional

from predictor.fine_tuning.config import DEFAULT_CLI_CONFIG, FINE_TUNING_CONFIG
from predictor.fine_tuning.training_pipeline import FineTuningTrainingPipeline
from predictor.fine_tuning.fine_tuned_indicator import FineTunedIndicatorModel
from db.database import get_db_manager
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class FineTuningCLI:
    """Command line interface for fine-tuning operations."""
    
    def __init__(self):
        self.db = get_db_manager()
        self.pipeline = FineTuningTrainingPipeline()
        
    def train_model(self, args):
        """Train a new fine-tuned model."""
        logger.info("Starting fine-tuning training")
        
        try:
            # Parse date arguments
            start_date = None
            end_date = None
            
            if args.start_date:
                start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
            
            if args.end_date:
                end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
            elif args.days_back:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=args.days_back)
            
            # Run training pipeline
            results = self.pipeline.run_complete_training_pipeline(
                start_date=start_date,
                end_date=end_date,
                max_samples=args.max_samples,
                job_name=args.job_name,
                enable_price_validation=args.enable_price_validation,
                wait_for_completion=args.wait_for_completion
            )
            
            print(f"Training pipeline completed successfully!")
            print(f"Job ID: {results['job_id']}")
            print(f"Pipeline ID: {results['pipeline_id']}")
            print(f"Duration: {results['total_duration_seconds']:.2f} seconds")
            
            if args.output_file:
                with open(args.output_file, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                print(f"Results saved to: {args.output_file}")
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            print(f"Error: {e}")
            sys.exit(1)
    
    def list_jobs(self, args):
        """List fine-tuning jobs."""
        try:
            jobs = self.db.fine_tuning_service.get_fine_tuning_jobs(
                api=args.api,
                status=args.status.split(',') if args.status else None,
                limit=args.limit
            )
            
            if not jobs:
                print("No fine-tuning jobs found.")
                return
            
            # Print job information
            print(f"{'Job ID':<36} {'Status':<12} {'Model':<20} {'Created':<20} {'Samples':<8}")
            print("-" * 100)
            
            for job in jobs:
                job_id = job['id'][:35] + '...' if len(job['id']) > 35 else job['id']
                status = job.get('status', 'unknown')
                model = job.get('model_name', 'unknown')[:19]
                created = job.get('created_at', '')
                if created:
                    created = created.strftime('%Y-%m-%d %H:%M') if hasattr(created, 'strftime') else str(created)[:16]
                samples = job.get('training_samples_count', 0)
                
                print(f"{job_id:<36} {status:<12} {model:<20} {created:<20} {samples:<8}")
            
        except Exception as e:
            logger.error(f"Error listing jobs: {e}")
            print(f"Error: {e}")
            sys.exit(1)
    
    def monitor_job(self, args):
        """Monitor a specific fine-tuning job."""
        try:
            job = self.pipeline.monitor_training_job(args.job_id)
            
            print(f"Job ID: {job['id']}")
            print(f"Status: {job['status']}")
            print(f"Model: {job.get('model_name', 'unknown')}")
            print(f"Created: {job.get('created_at', 'unknown')}")
            print(f"Started: {job.get('started_at', 'not started')}")
            print(f"Completed: {job.get('completed_at', 'not completed')}")
            print(f"Training Samples: {job.get('training_samples_count', 0)}")
            
            if job.get('error_message'):
                print(f"Error: {job['error_message']}")
            
            if job.get('final_metrics'):
                print("\nFinal Metrics:")
                for key, value in job['final_metrics'].items():
                    print(f"  {key}: {value}")
            
            if job.get('validation_metrics'):
                print("\nValidation Metrics:")
                for key, value in job['validation_metrics'].items():
                    print(f"  {key}: {value}")
            
        except Exception as e:
            logger.error(f"Error monitoring job: {e}")
            print(f"Error: {e}")
            sys.exit(1)
    
    def test_model(self, args):
        """Test a fine-tuned model with sample text."""
        try:
            # Load model
            model = FineTunedIndicatorModel()
            
            if args.job_id:
                success = model.load_from_job(args.job_id)
            elif args.model_name:
                success = model.load_model(args.model_name)
            else:
                print("Error: Must specify either --job-id or --model-name")
                sys.exit(1)
            
            if not success:
                print("Error: Failed to load model")
                sys.exit(1)
            
            # Get test text
            if args.text_file:
                with open(args.text_file, 'r') as f:
                    test_text = f.read()
            else:
                test_text = args.text or "Sample financial news article for testing."
            
            # Make prediction
            result = model.predict_single(
                test_text,
                {'title': args.title or 'Test Article', 'source': args.source or 'CLI Test'}
            )
            
            print(f"Model: {result['model_name']}")
            print(f"Prediction: {result['prediction']}")
            print(f"Confidence: {result['confidence']:.3f}")
            print(f"Influence Score: {result.get('influence_score', 'N/A')}")
            print(f"Influence Label: {result.get('influence_label', 'N/A')}")
            
        except Exception as e:
            logger.error(f"Error testing model: {e}")
            print(f"Error: {e}")
            sys.exit(1)
    
    def list_models(self, args):
        """List available fine-tuned models."""
        try:
            model = FineTunedIndicatorModel()
            models = model.list_available_models()
            
            if not models:
                print("No fine-tuned models found.")
                return
            
            print(f"{'Job ID':<36} {'Model Name':<25} {'Base Model':<20} {'Samples':<8} {'Completed':<20}")
            print("-" * 115)
            
            for model_info in models:
                job_id = model_info['job_id'][:35] + '...' if len(model_info['job_id']) > 35 else model_info['job_id']
                model_name = model_info['model_name'][:24]
                base_model = model_info['base_model'][:19] if model_info['base_model'] else 'unknown'
                samples = model_info.get('training_samples', 0)
                completed = model_info.get('completed_at', '')
                if completed:
                    completed = completed.strftime('%Y-%m-%d %H:%M') if hasattr(completed, 'strftime') else str(completed)[:16]
                
                print(f"{job_id:<36} {model_name:<25} {base_model:<20} {samples:<8} {completed:<20}")
            
        except Exception as e:
            logger.error(f"Error listing models: {e}")
            print(f"Error: {e}")
            sys.exit(1)
    
    def get_statistics(self, args):
        """Get fine-tuning statistics."""
        try:
            stats = self.db.fine_tuning_service.get_job_statistics(args.api)
            
            print("Fine-Tuning Statistics")
            print("=" * 30)
            print(f"Total Jobs: {stats['total_jobs']}")
            print(f"Total Cost: ${stats['total_cost']:.2f}")
            print(f"Total Tokens: {stats['total_tokens']:,}")
            
            print("\nBy Status:")
            for status, status_stats in stats['by_status'].items():
                print(f"  {status}: {status_stats['count']} jobs, ${status_stats['cost']:.2f}, {status_stats['tokens']:,} tokens")
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            print(f"Error: {e}")
            sys.exit(1)


def create_parser():
    """Create the argument parser for the CLI."""
    parser = argparse.ArgumentParser(
        description='Fine-Tuning CLI for LLM-based Financial Indicator Models',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Train command
    train_parser = subparsers.add_parser('train', help='Train a new fine-tuned model')
    train_parser.add_argument('--start-date', help='Start date for training data (YYYY-MM-DD)')
    train_parser.add_argument('--end-date', help='End date for training data (YYYY-MM-DD)')
    train_parser.add_argument('--days-back', type=int, help='Number of days back from now for training data')
    train_parser.add_argument('--max-samples', type=int, default=DEFAULT_CLI_CONFIG['max_samples'],
                             help='Maximum number of training samples')
    train_parser.add_argument('--job-name', help='Name for the training job')
    train_parser.add_argument('--enable-price-validation', action='store_true', 
                             default=DEFAULT_CLI_CONFIG['enable_price_validation'],
                             help='Enable price validation filtering')
    train_parser.add_argument('--wait-for-completion', action='store_true',
                             help='Wait for training completion')
    train_parser.add_argument('--output-file', help='File to save training results')
    
    # List jobs command
    list_parser = subparsers.add_parser('list-jobs', help='List fine-tuning jobs')
    list_parser.add_argument('--api', default='gemini', help='API to filter by')
    list_parser.add_argument('--status', help='Comma-separated list of statuses to filter by')
    list_parser.add_argument('--limit', type=int, default=20, help='Maximum number of jobs to list')
    
    # Monitor command
    monitor_parser = subparsers.add_parser('monitor', help='Monitor a fine-tuning job')
    monitor_parser.add_argument('job_id', help='ID of the job to monitor')
    
    # Test model command
    test_parser = subparsers.add_parser('test', help='Test a fine-tuned model')
    test_group = test_parser.add_mutually_exclusive_group(required=True)
    test_group.add_argument('--job-id', help='Job ID of the model to test')
    test_group.add_argument('--model-name', help='Name of the model to test')
    test_parser.add_argument('--text', help='Text to analyze')
    test_parser.add_argument('--text-file', help='File containing text to analyze')
    test_parser.add_argument('--title', help='Article title for context')
    test_parser.add_argument('--source', help='Article source for context')
    
    # List models command
    models_parser = subparsers.add_parser('list-models', help='List available fine-tuned models')
    
    # Statistics command
    stats_parser = subparsers.add_parser('stats', help='Get fine-tuning statistics')
    stats_parser.add_argument('--api', help='API to filter statistics by')
    
    return parser


def main():
    """Main CLI entry point."""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    cli = FineTuningCLI()
    
    # Route to appropriate command handler
    if args.command == 'train':
        cli.train_model(args)
    elif args.command == 'list-jobs':
        cli.list_jobs(args)
    elif args.command == 'monitor':
        cli.monitor_job(args)
    elif args.command == 'test':
        cli.test_model(args)
    elif args.command == 'list-models':
        cli.list_models(args)
    elif args.command == 'stats':
        cli.get_statistics(args)
    else:
        print(f"Unknown command: {args.command}")
        sys.exit(1)


if __name__ == '__main__':
    main()
