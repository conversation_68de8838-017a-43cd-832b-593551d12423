"""
Fine-Tuning Module for LLM-based Financial Indicator Models

This module provides infrastructure for fine-tuning Large Language Models
on financial news data with influence scores for market prediction.

Components:
- config: Configuration management for fine-tuning parameters
- data_pipeline: Data extraction and preparation for training
- training_pipeline: Fine-tuning job management and execution
- price_validator: Price validation and data quality checks
- gemini_fine_tuner: Gemini-specific fine-tuning implementation
- fine_tuned_indicator: Integration with existing predictor architecture
- cli: Command-line interface for fine-tuning operations
"""

__version__ = "0.1.0"

from .config import (
    FINE_TUNING_CONFIG,
    DEFAULT_CLI_CONFIG,
    DATA_CONFIG,
    MODEL_CONFIG,
    TRAINING_CONFIG
)

from .training_pipeline import FineTuningTrainingPipeline
from .fine_tuned_indicator import FineTunedIndicatorModel
from .data_pipeline import TrainingDataPipeline
from .price_validator import PriceValidationFilter
from .gemini_fine_tuner import GeminiFineTuner

__all__ = [
    'FINE_TUNING_CONFIG',
    'DEFAULT_CLI_CONFIG',
    'DATA_CONFIG',
    'MODEL_CONFIG',
    'TRAINING_CONFIG',
    'FineTuningTrainingPipeline',
    'FineTunedIndicatorModel',
    'TrainingDataPipeline',
    'PriceValidationFilter',
    'GeminiFineTuner'
]
