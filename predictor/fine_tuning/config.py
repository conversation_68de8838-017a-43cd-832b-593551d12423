"""
Configuration settings for LLM fine-tuning operations.

This module contains all configuration parameters for fine-tuning LLMs
on financial news data with influence scores.
"""

import os
from pathlib import Path
from typing import Dict, List, Any

# Base paths
ROOT_DIR = Path(__file__).parent.parent.parent
FINE_TUNING_DIR = Path(__file__).parent
OUTPUT_DIR = FINE_TUNING_DIR / "output"
MODELS_DIR = OUTPUT_DIR / "models"
DATA_DIR = OUTPUT_DIR / "data"
LOGS_DIR = OUTPUT_DIR / "logs"

# Ensure directories exist
for directory in [OUTPUT_DIR, MODELS_DIR, DATA_DIR, LOGS_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Date format
DATE_FORMAT = "%Y-%m-%d"

# Data configuration
DATA_CONFIG = {
    # Influence score validation
    'min_influence_score': -5,
    'max_influence_score': 5,
    'valid_influence_scores': list(range(-5, 6)),  # -5 to 5 inclusive
    
    # Data filtering
    'min_article_length': 100,  # Minimum words in article content
    'max_article_length': 2000,  # Maximum words in article content
    'exclude_sources': [],  # Sources to exclude from training
    'required_fields': ['title', 'content', 'date'],  # Required article fields
    
    # Price validation
    'price_validation_enabled': True,
    'price_validation_days': 3,  # Days to look ahead for price validation
    'price_correlation_threshold': 0.1,  # Minimum correlation for validation
    'spy_ticker': 'SPY',
    
    # Data splits
    'train_split': 0.8,
    'validation_split': 0.1,
    'test_split': 0.1,
    
    # Sampling and balancing
    'balance_classes': True,
    'max_samples_per_class': 1000,
    'min_samples_per_class': 50,
}

# Model configuration
MODEL_CONFIG = {
    # Base model settings
    'base_model': 'gemini-2.0-flash-001',
    'api_name': 'gemini',
    
    # Fine-tuning parameters
    'learning_rate': 0.001,
    'batch_size': 32,
    'num_epochs': 3,
    'validation_frequency': 100,  # Steps between validation
    
    # Model architecture
    'max_input_length': 1024,
    'temperature': 0.7,
    'top_p': 0.9,
    
    # Training optimization
    'gradient_accumulation_steps': 4,
    'warmup_steps': 100,
    'weight_decay': 0.01,
    'early_stopping_patience': 3,
}

# Training job configuration
TRAINING_CONFIG = {
    # Job management
    'max_concurrent_jobs': 2,
    'job_timeout_hours': 24,
    'retry_failed_jobs': True,
    'max_retries': 3,
    
    # Data preparation
    'data_format': 'jsonl',  # Format for training data files
    'include_metadata': True,
    'shuffle_data': True,
    'seed': 42,
    
    # Validation and metrics
    'validation_metrics': ['accuracy', 'precision', 'recall', 'f1'],
    'save_best_model': True,
    'save_checkpoints': True,
    'checkpoint_frequency': 500,  # Steps between checkpoints
}

# Google Cloud Storage configuration
GCS_CONFIG = {
    'bucket_name': os.getenv('GOOGLE_CLOUD_BUCKET', 'newsmonitor-fine-tuning'),
    'training_data_prefix': 'fine-tuning/training-data',
    'model_output_prefix': 'fine-tuning/models',
    'temp_data_prefix': 'fine-tuning/temp',
}

# API configuration
API_CONFIG = {
    'gemini': {
        'project_id': os.getenv('GOOGLE_CLOUD_PROJECT'),
        'location': os.getenv('GOOGLE_CLOUD_LOCATION', 'us-central1'),
        'use_vertex_ai': True,
        'max_requests_per_minute': 30,
        'max_tokens_per_minute': 100000,
        'budget_limit': 100.0,  # USD
    }
}

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'log_to_file': True,
    'log_to_console': True,
    'max_log_size_mb': 100,
    'backup_count': 5,
}

# Influence score mapping for classification
INFLUENCE_SCORE_MAPPING = {
    # Map influence scores to classification labels
    'score_to_label': {
        -5: 'very_negative',
        -4: 'very_negative', 
        -3: 'negative',
        -2: 'negative',
        -1: 'slightly_negative',
        0: 'neutral',
        1: 'slightly_positive',
        2: 'positive',
        3: 'positive',
        4: 'very_positive',
        5: 'very_positive'
    },
    
    # Simplified 3-class mapping
    'score_to_simple_label': {
        -5: 'negative', -4: 'negative', -3: 'negative',
        -2: 'negative', -1: 'negative',
        0: 'neutral',
        1: 'positive', 2: 'positive', 3: 'positive',
        4: 'positive', 5: 'positive'
    },
    
    # Numeric mapping for regression
    'score_to_numeric': {i: float(i) for i in range(-5, 6)},
}

# Training data format templates
TRAINING_DATA_TEMPLATES = {
    'classification': {
        'system_prompt': """You are a financial news analyst. Analyze the given news article and predict its influence on the S&P 500 index. 
Classify the influence as: very_negative, negative, slightly_negative, neutral, slightly_positive, positive, or very_positive.""",
        
        'user_prompt_template': """Article Title: {title}

Article Content: {content}

Source: {source}
Date: {date}

Analyze this financial news article and classify its potential influence on the S&P 500 index.""",
        
        'assistant_response_template': "{influence_label}"
    },
    
    'regression': {
        'system_prompt': """You are a financial news analyst. Analyze the given news article and predict its influence on the S&P 500 index.
Provide a numerical influence score from -5 (very negative) to +5 (very positive), where 0 is neutral.""",
        
        'user_prompt_template': """Article Title: {title}

Article Content: {content}

Source: {source}
Date: {date}

Analyze this financial news article and provide a numerical influence score from -5 to +5.""",
        
        'assistant_response_template': "{influence_score}"
    }
}

# Validation configuration
VALIDATION_CONFIG = {
    # Price movement validation
    'price_validation': {
        'enabled': True,
        'lookback_days': [1, 2, 3, 5],  # Days to check price movements
        'correlation_threshold': 0.1,
        'direction_match_threshold': 0.6,  # Minimum % of direction matches
    },
    
    # Data quality validation
    'data_quality': {
        'min_article_words': 50,
        'max_article_words': 3000,
        'required_metadata_fields': ['influence_tagging'],
        'exclude_duplicate_content': True,
        'content_similarity_threshold': 0.9,
    },
    
    # Training validation
    'training_validation': {
        'min_training_samples': 100,
        'min_validation_samples': 20,
        'max_class_imbalance_ratio': 10.0,
        'validate_data_format': True,
    }
}

# Export main configuration
FINE_TUNING_CONFIG = {
    'data': DATA_CONFIG,
    'model': MODEL_CONFIG,
    'training': TRAINING_CONFIG,
    'gcs': GCS_CONFIG,
    'api': API_CONFIG,
    'logging': LOGGING_CONFIG,
    'influence_mapping': INFLUENCE_SCORE_MAPPING,
    'templates': TRAINING_DATA_TEMPLATES,
    'validation': VALIDATION_CONFIG,
}

# Default configuration for CLI
DEFAULT_CLI_CONFIG = {
    'api': 'gemini',
    'model': 'gemini-2.0-flash-001',
    'max_samples': 1000,
    'validation_days': 3,
    'enable_price_validation': True,
    'output_format': 'classification',  # or 'regression'
    'balance_classes': True,
}
