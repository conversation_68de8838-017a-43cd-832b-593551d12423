"""
Training Data Pipeline for LLM Fine-Tuning

This module handles extraction, validation, and preparation of training data
from the NewsMonitor database for fine-tuning LLM models on influence prediction.
"""

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path

from db.database import get_db_manager
from predictor.fine_tuning.config import (
    DATA_CONFIG, 
    INFLUENCE_SCORE_MAPPING,
    TRAINING_DATA_TEMPLATES,
    VALIDATION_CONFIG
)
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class TrainingDataExtractor:
    """Extracts and validates training data from the database."""
    
    def __init__(self, db_manager=None):
        self.db = db_manager or get_db_manager()
        self.data_config = DATA_CONFIG
        self.validation_config = VALIDATION_CONFIG['data_quality']
        
    def extract_articles_with_influence(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Extract articles with influence scores from the database.
        
        Args:
            start_date: Start date for article filtering
            end_date: End date for article filtering  
            limit: Maximum number of articles to extract
            
        Returns:
            List of articles with influence metadata
        """
        logger.info(f"Extracting articles with influence scores from {start_date} to {end_date}")
        
        # Get articles with influence tagging
        articles = self.db.article_service.get_articles_by_influence(
            start_date=start_date,
            end_date=end_date,
            influence_field="influence_tagging.influence",
            limit=limit
        )
        
        logger.info(f"Retrieved {len(articles)} articles from database")
        return articles
    
    def validate_influence_score(self, influence_score: Any) -> bool:
        """
        Validate that influence score is within expected range.
        
        Args:
            influence_score: The influence score to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not isinstance(influence_score, (int, float)):
            return False
            
        return (self.data_config['min_influence_score'] <= 
                influence_score <= 
                self.data_config['max_influence_score'])
    
    def validate_article_content(self, article: Dict[str, Any]) -> bool:
        """
        Validate article content meets quality requirements.
        
        Args:
            article: Article data dictionary
            
        Returns:
            True if article passes validation, False otherwise
        """
        # Check required fields
        for field in self.data_config['required_fields']:
            if not article.get(field):
                return False
        
        # Check content length
        content = article.get('content', '')
        if not content:
            return False
            
        word_count = len(content.split())
        if (word_count < self.validation_config['min_article_words'] or 
            word_count > self.validation_config['max_article_words']):
            return False
        
        # Check for required metadata
        article_metadata = article.get('article_metadata', {})
        if not article_metadata.get('influence_tagging'):
            return False
            
        return True
    
    def extract_influence_data(self, article: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract influence data from article metadata.
        
        Args:
            article: Article data dictionary
            
        Returns:
            Influence data dictionary or None if invalid
        """
        try:
            metadata = article.get('article_metadata', {})
            influence_tagging = metadata.get('influence_tagging', {})
            
            influence_score = influence_tagging.get('influence')
            if influence_score is None:
                return None
                
            # Validate influence score
            if not self.validate_influence_score(influence_score):
                logger.warning(f"Invalid influence score {influence_score} for article {article.get('id')}")
                return None
            
            return {
                'influence_score': int(influence_score),
                'category': influence_tagging.get('category', ''),
                'tags': influence_tagging.get('tags', []),
                'regions': influence_tagging.get('regions', []),
                'reason': influence_tagging.get('reason', '')
            }
            
        except Exception as e:
            logger.error(f"Error extracting influence data from article {article.get('id')}: {e}")
            return None


class TrainingDataFormatter:
    """Formats extracted data for fine-tuning."""
    
    def __init__(self, output_format: str = 'classification'):
        self.output_format = output_format
        self.templates = TRAINING_DATA_TEMPLATES[output_format]
        self.score_mapping = INFLUENCE_SCORE_MAPPING
        
    def format_training_sample(self, article: Dict[str, Any], influence_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format a single training sample for fine-tuning.
        
        Args:
            article: Article data dictionary
            influence_data: Influence data dictionary
            
        Returns:
            Formatted training sample
        """
        # Prepare article data
        title = article.get('title', '').strip()
        content = article.get('content', '').strip()
        source = article.get('source', '').strip()
        date = article.get('date', '')
        
        # Format date if it's a datetime object
        if hasattr(date, 'strftime'):
            date = date.strftime('%Y-%m-%d')
        
        # Create user prompt
        user_prompt = self.templates['user_prompt_template'].format(
            title=title,
            content=content,
            source=source,
            date=date
        )
        
        # Create assistant response based on output format
        influence_score = influence_data['influence_score']
        
        if self.output_format == 'classification':
            influence_label = self.score_mapping['score_to_label'][influence_score]
            assistant_response = self.templates['assistant_response_template'].format(
                influence_label=influence_label
            )
        else:  # regression
            assistant_response = self.templates['assistant_response_template'].format(
                influence_score=influence_score
            )
        
        # Create training sample
        training_sample = {
            'messages': [
                {
                    'role': 'system',
                    'content': self.templates['system_prompt']
                },
                {
                    'role': 'user', 
                    'content': user_prompt
                },
                {
                    'role': 'assistant',
                    'content': assistant_response
                }
            ],
            'metadata': {
                'article_id': article.get('id'),
                'influence_score': influence_score,
                'influence_label': self.score_mapping['score_to_label'].get(influence_score),
                'source': source,
                'date': date,
                'category': influence_data.get('category'),
                'tags': influence_data.get('tags', []),
                'regions': influence_data.get('regions', [])
            }
        }
        
        return training_sample
    
    def create_training_dataset(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create a complete training dataset from articles.
        
        Args:
            articles: List of article dictionaries
            
        Returns:
            List of formatted training samples
        """
        training_samples = []
        extractor = TrainingDataExtractor()
        
        for article in articles:
            # Validate article
            if not extractor.validate_article_content(article):
                continue
                
            # Extract influence data
            influence_data = extractor.extract_influence_data(article)
            if not influence_data:
                continue
                
            # Format training sample
            try:
                sample = self.format_training_sample(article, influence_data)
                training_samples.append(sample)
            except Exception as e:
                logger.error(f"Error formatting training sample for article {article.get('id')}: {e}")
                continue
        
        logger.info(f"Created {len(training_samples)} training samples from {len(articles)} articles")
        return training_samples


class TrainingDataPipeline:
    """Main pipeline for preparing training data."""
    
    def __init__(self, output_format: str = 'classification'):
        self.extractor = TrainingDataExtractor()
        self.formatter = TrainingDataFormatter(output_format)
        self.output_format = output_format
        
    def prepare_training_data(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        max_samples: Optional[int] = None,
        balance_classes: bool = True
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Prepare complete training dataset.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            max_samples: Maximum number of samples
            balance_classes: Whether to balance class distribution
            
        Returns:
            Tuple of (training_samples, statistics)
        """
        logger.info("Starting training data preparation pipeline")
        
        # Extract articles
        articles = self.extractor.extract_articles_with_influence(
            start_date=start_date,
            end_date=end_date,
            limit=max_samples
        )
        
        # Format training samples
        training_samples = self.formatter.create_training_dataset(articles)
        
        # Balance classes if requested
        if balance_classes and self.output_format == 'classification':
            training_samples = self._balance_classes(training_samples)
        
        # Generate statistics
        stats = self._generate_statistics(training_samples)
        
        logger.info(f"Training data preparation complete: {len(training_samples)} samples")
        return training_samples, stats
    
    def _balance_classes(self, samples: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Balance class distribution in training samples."""
        from collections import defaultdict
        import random
        
        # Group samples by influence label
        class_samples = defaultdict(list)
        for sample in samples:
            label = sample['metadata']['influence_label']
            class_samples[label].append(sample)
        
        # Find minimum class size
        min_class_size = min(len(samples) for samples in class_samples.values())
        max_samples_per_class = min(min_class_size, DATA_CONFIG['max_samples_per_class'])
        
        # Sample from each class
        balanced_samples = []
        for label, label_samples in class_samples.items():
            if len(label_samples) > max_samples_per_class:
                sampled = random.sample(label_samples, max_samples_per_class)
            else:
                sampled = label_samples
            balanced_samples.extend(sampled)
        
        logger.info(f"Balanced dataset: {len(balanced_samples)} samples across {len(class_samples)} classes")
        return balanced_samples
    
    def _generate_statistics(self, samples: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate statistics about the training dataset."""
        from collections import Counter
        
        stats = {
            'total_samples': len(samples),
            'influence_score_distribution': Counter(),
            'influence_label_distribution': Counter(),
            'source_distribution': Counter(),
            'date_range': {'min': None, 'max': None}
        }
        
        dates = []
        for sample in samples:
            metadata = sample['metadata']
            
            # Count distributions
            stats['influence_score_distribution'][metadata['influence_score']] += 1
            if metadata.get('influence_label'):
                stats['influence_label_distribution'][metadata['influence_label']] += 1
            if metadata.get('source'):
                stats['source_distribution'][metadata['source']] += 1
            
            # Track date range
            if metadata.get('date'):
                dates.append(metadata['date'])
        
        if dates:
            stats['date_range']['min'] = min(dates)
            stats['date_range']['max'] = max(dates)
        
        return stats
