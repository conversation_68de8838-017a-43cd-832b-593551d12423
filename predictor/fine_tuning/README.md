# Fine-Tuned LLM Indicator Model

This module implements a comprehensive fine-tuning infrastructure for Large Language Models (LLMs) to predict financial market movements based on news articles with influence scores.

## Overview

The fine-tuning system extends the existing NewsMonitor infrastructure to support:

- **Data Pipeline**: Extracts articles with influence scores from the database
- **Price Validation**: Filters training data based on actual SPY price movements
- **Fine-Tuning**: Uses Google Gemini API with native batch processing
- **Model Integration**: Seamlessly integrates with existing predictor architecture
- **CLI Interface**: Comprehensive command-line tools for training and management

## Architecture

### Core Components

1. **Data Pipeline** (`data_pipeline.py`)
   - Extracts articles with influence scores (-5 to +5)
   - Validates data quality and format
   - Formats data for fine-tuning (classification or regression)

2. **Price Validator** (`price_validator.py`)
   - Validates influence scores against actual price movements
   - Configurable validation window (X days after article)
   - Filters training samples based on correlation

3. **Gemini Fine-Tuner** (`gemini_fine_tuner.py`)
   - Extends existing GeminiManager with fine-tuning capabilities
   - Uses google-genai SDK with native batch API
   - Manages training data upload and job tracking

4. **Training Pipeline** (`training_pipeline.py`)
   - Orchestrates complete fine-tuning process
   - Handles data preparation, validation, and job creation
   - Provides comprehensive error handling and logging

5. **Fine-Tuned Indicator** (`fine_tuned_indicator.py`)
   - Integrates fine-tuned models with existing architecture
   - Provides consistent interface for predictions
   - Supports both single and batch predictions

## Configuration

The system uses a comprehensive configuration system in `config.py`:

```python
# Data configuration
DATA_CONFIG = {
    'min_influence_score': -5,
    'max_influence_score': 5,
    'price_validation_enabled': True,
    'price_validation_days': 3,
    'balance_classes': True,
}

# Model configuration
MODEL_CONFIG = {
    'base_model': 'gemini-2.0-flash-001',
    'learning_rate': 0.001,
    'batch_size': 32,
    'num_epochs': 3,
}
```

## Usage

### Command Line Interface

The module provides a comprehensive CLI for all operations:

```bash
# Train a new model
python -m predictor.fine_tuning train --days-back 30 --max-samples 1000 --enable-price-validation

# List training jobs
python -m predictor.fine_tuning list-jobs --status completed

# Monitor a specific job
python -m predictor.fine_tuning monitor <job_id>

# Test a trained model
python -m predictor.fine_tuning test --job-id <job_id> --text "Financial news article text"

# List available models
python -m predictor.fine_tuning list-models

# Get statistics
python -m predictor.fine_tuning stats
```

### Programmatic Usage

```python
from predictor.fine_tuning import FineTuningTrainingPipeline, FineTunedIndicatorModel

# Train a new model
pipeline = FineTuningTrainingPipeline()
results = pipeline.run_complete_training_pipeline(
    max_samples=1000,
    enable_price_validation=True,
    wait_for_completion=True
)

# Use a trained model
model = FineTunedIndicatorModel()
model.load_from_job(results['job_id'])

prediction = model.predict_single(
    "Breaking: Company reports record earnings",
    {'title': 'Earnings Report', 'source': 'Financial News'}
)
```

## Data Format

### Training Data Format

The system uses a messages format compatible with fine-tuning APIs:

```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a financial news analyst..."
    },
    {
      "role": "user", 
      "content": "Article Title: ...\nArticle Content: ..."
    },
    {
      "role": "assistant",
      "content": "positive"
    }
  ],
  "metadata": {
    "article_id": "...",
    "influence_score": 2,
    "influence_label": "positive"
  }
}
```

### Influence Score Mapping

- **-5 to -3**: Very negative / Negative
- **-2 to -1**: Slightly negative / Negative  
- **0**: Neutral
- **1 to 2**: Slightly positive / Positive
- **3 to 5**: Positive / Very positive

## Price Validation

The price validation filter ensures training data quality by:

1. **Direction Matching**: Influence direction matches price movement direction
2. **Correlation Threshold**: Minimum correlation between influence and price change
3. **Configurable Window**: Validate against price movements X days after article

Example validation:
- Article with influence score +3 (positive)
- SPY price increases 2% in next 3 days
- ✅ Validation passes (direction match + correlation)

## Database Schema

The system adds new database models for tracking:

```sql
-- Fine-tuning jobs tracking
CREATE TABLE llm_fine_tuning_jobs (
    id VARCHAR PRIMARY KEY,
    api VARCHAR NOT NULL,
    model_name VARCHAR NOT NULL,
    status VARCHAR NOT NULL,
    training_config JSONB,
    data_config JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    -- ... additional fields
);
```

## Integration with Existing System

The fine-tuned models integrate seamlessly with:

- **Web Interface**: Predictions displayed alongside traditional models
- **Predictor Module**: Consistent interface with existing indicator models
- **Database**: Uses existing article and market data infrastructure
- **Logging**: Follows established logging patterns

## Testing

Comprehensive test suite includes:

```bash
# Run all tests
python -m pytest predictor/fine_tuning/tests/

# Run specific test
python -m pytest predictor/fine_tuning/tests/test_fine_tuning_pipeline.py
```

## Monitoring and Logging

- **Job Tracking**: Database tracking of all training jobs
- **Progress Monitoring**: Real-time status updates
- **Comprehensive Logging**: Detailed logs for debugging
- **Error Handling**: Graceful error handling with recovery

## Performance Considerations

- **Batch Processing**: Uses native Google batch API for efficiency
- **Data Validation**: Early validation to prevent failed training jobs
- **Resource Management**: Configurable limits and timeouts
- **Cost Tracking**: Monitors API usage and costs

## Future Enhancements

- **Multi-API Support**: Support for OpenAI and Anthropic fine-tuning
- **Advanced Validation**: More sophisticated price correlation metrics
- **Model Versioning**: Version management for fine-tuned models
- **A/B Testing**: Framework for comparing model performance
- **Real-time Training**: Continuous learning from new data
