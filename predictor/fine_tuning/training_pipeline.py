"""
Fine-Tuning Training Pipeline

This module orchestrates the complete fine-tuning process including data preparation,
model training, validation, and result storage with comprehensive error handling
and progress tracking.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from db.database import get_db_manager
from predictor.fine_tuning.config import (
    FINE_TUNING_CONFIG,
    MODEL_CONFIG,
    TRAINING_CONFIG,
    DATA_CONFIG
)
from predictor.fine_tuning.data_pipeline import TrainingDataPipeline
from predictor.fine_tuning.price_validator import PriceValidationFilter
from predictor.fine_tuning.gemini_fine_tuner import GeminiFineTuner
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class FineTuningTrainingPipeline:
    """Main pipeline for orchestrating LLM fine-tuning operations."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or FINE_TUNING_CONFIG
        self.db = get_db_manager()
        
        # Initialize components
        self.data_pipeline = TrainingDataPipeline(
            output_format=self.config.get('output_format', 'classification')
        )
        self.price_validator = PriceValidationFilter(
            self.config['validation']['price_validation']
        )
        self.fine_tuner = GeminiFineTuner()
        
    def prepare_training_data(self, 
                            start_date: Optional[datetime] = None,
                            end_date: Optional[datetime] = None,
                            max_samples: Optional[int] = None,
                            enable_price_validation: bool = True) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Prepare and validate training data for fine-tuning.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            max_samples: Maximum number of samples
            enable_price_validation: Whether to apply price validation
            
        Returns:
            Tuple of (training_samples, preparation_stats)
        """
        logger.info("Starting training data preparation")
        
        try:
            # Extract and format training data
            training_samples, data_stats = self.data_pipeline.prepare_training_data(
                start_date=start_date,
                end_date=end_date,
                max_samples=max_samples,
                balance_classes=DATA_CONFIG['balance_classes']
            )
            
            logger.info(f"Extracted {len(training_samples)} training samples")
            
            # Apply price validation if enabled
            if enable_price_validation:
                validation_days = DATA_CONFIG.get('price_validation_days', 3)
                training_samples, validation_stats = self.price_validator.filter_training_samples(
                    training_samples, validation_days
                )
                
                logger.info(f"Price validation complete: {len(training_samples)} samples passed")
                data_stats['price_validation'] = validation_stats
            
            # Validate data format for fine-tuning
            format_validation = self.fine_tuner.validate_training_data_format(training_samples)
            if not format_validation['is_valid']:
                raise ValueError(f"Training data format validation failed: {format_validation['errors']}")
            
            data_stats['format_validation'] = format_validation
            
            logger.info(f"Training data preparation complete: {len(training_samples)} samples ready")
            return training_samples, data_stats
            
        except Exception as e:
            logger.error(f"Error in training data preparation: {e}")
            raise
    
    def create_fine_tuning_job(self,
                              training_samples: List[Dict[str, Any]],
                              job_name: Optional[str] = None,
                              base_model: Optional[str] = None,
                              training_config: Optional[Dict[str, Any]] = None) -> str:
        """
        Create and start a fine-tuning job.
        
        Args:
            training_samples: Prepared training samples
            job_name: Optional name for the job
            base_model: Base model to fine-tune
            training_config: Training configuration overrides
            
        Returns:
            Job ID of the created fine-tuning job
        """
        logger.info("Creating fine-tuning job")
        
        try:
            # Generate job ID and name
            job_id = str(uuid.uuid4())
            if not job_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                job_name = f"influence_predictor_{timestamp}"
            
            base_model = base_model or MODEL_CONFIG['base_model']
            training_config = training_config or {}
            
            # Merge with default training config
            final_training_config = {**MODEL_CONFIG, **training_config}
            
            # Upload training data
            logger.info("Uploading training data to cloud storage")
            training_data_uri = self.fine_tuner.upload_training_data(
                training_samples, 
                dataset_name=f"{job_name}_training_data"
            )
            
            # Create database record
            job_data = {
                'id': job_id,
                'api': 'gemini',
                'model_name': base_model,
                'job_type': 'fine_tuning',
                'status': 'pending',
                'training_config': final_training_config,
                'data_config': {
                    'training_samples_count': len(training_samples),
                    'data_source': 'newsmonitor_articles',
                    'influence_score_range': [-5, 5],
                    'output_format': self.data_pipeline.output_format
                },
                'training_data_uri': training_data_uri,
                'created_at': datetime.now(timezone.utc)
            }
            
            # Store job in database
            self.db.fine_tuning_service.create_fine_tuning_job(job_data)
            
            # Create fine-tuning job with API
            api_job_data = self.fine_tuner.create_fine_tuning_job(
                training_data_uri=training_data_uri,
                base_model=base_model,
                job_config=final_training_config
            )
            
            # Update database with API job information
            self.db.fine_tuning_service.update_fine_tuning_job(job_id, {
                'raw_response': api_job_data,
                'status': 'running',
                'started_at': datetime.now(timezone.utc)
            })
            
            logger.info(f"Fine-tuning job created successfully: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Error creating fine-tuning job: {e}")
            # Mark job as failed if it was created in database
            if 'job_id' in locals():
                self.db.fine_tuning_service.mark_job_failed(job_id, str(e))
            raise
    
    def monitor_training_job(self, job_id: str) -> Dict[str, Any]:
        """
        Monitor the progress of a fine-tuning job.
        
        Args:
            job_id: ID of the job to monitor
            
        Returns:
            Current job status and metrics
        """
        try:
            # Get job from database
            job = self.db.fine_tuning_service.get_fine_tuning_job(job_id)
            if not job:
                raise ValueError(f"Job {job_id} not found")
            
            # Get status from API if job is active
            if job['status'] in ['pending', 'running']:
                api_status = self.fine_tuner.get_fine_tuning_job_status(job_id)
                
                # Update database with latest status
                updates = {
                    'status': api_status.get('status', job['status'])
                }
                
                if api_status.get('metrics'):
                    updates['validation_metrics'] = api_status['metrics']
                
                if api_status.get('error_message'):
                    updates['error_message'] = api_status['error_message']
                
                # Mark as completed if finished
                if api_status.get('status') == 'completed':
                    updates['completed_at'] = datetime.now(timezone.utc)
                    updates['final_metrics'] = api_status.get('metrics', {})
                    updates['fine_tuned_model_name'] = api_status.get('fine_tuned_model_name')
                
                # Mark as failed if error occurred
                elif api_status.get('status') == 'failed':
                    updates['completed_at'] = datetime.now(timezone.utc)
                    updates['error_message'] = api_status.get('error_message', 'Unknown error')
                
                self.db.fine_tuning_service.update_fine_tuning_job(job_id, updates)
                
                # Refresh job data
                job = self.db.fine_tuning_service.get_fine_tuning_job(job_id)
            
            return job
            
        except Exception as e:
            logger.error(f"Error monitoring training job {job_id}: {e}")
            raise
    
    def run_complete_training_pipeline(self,
                                     start_date: Optional[datetime] = None,
                                     end_date: Optional[datetime] = None,
                                     max_samples: Optional[int] = None,
                                     job_name: Optional[str] = None,
                                     enable_price_validation: bool = True,
                                     wait_for_completion: bool = False) -> Dict[str, Any]:
        """
        Run the complete fine-tuning pipeline from data preparation to job creation.
        
        Args:
            start_date: Start date for data extraction
            end_date: End date for data extraction
            max_samples: Maximum number of samples
            job_name: Optional name for the job
            enable_price_validation: Whether to apply price validation
            wait_for_completion: Whether to wait for job completion
            
        Returns:
            Pipeline execution results
        """
        logger.info("Starting complete fine-tuning pipeline")
        
        pipeline_start_time = datetime.now()
        results = {
            'pipeline_id': str(uuid.uuid4()),
            'started_at': pipeline_start_time,
            'status': 'running',
            'stages': {}
        }
        
        try:
            # Stage 1: Data preparation
            logger.info("Stage 1: Preparing training data")
            stage_start = datetime.now()
            
            training_samples, data_stats = self.prepare_training_data(
                start_date=start_date,
                end_date=end_date,
                max_samples=max_samples,
                enable_price_validation=enable_price_validation
            )
            
            results['stages']['data_preparation'] = {
                'status': 'completed',
                'duration_seconds': (datetime.now() - stage_start).total_seconds(),
                'statistics': data_stats,
                'samples_count': len(training_samples)
            }
            
            # Stage 2: Job creation
            logger.info("Stage 2: Creating fine-tuning job")
            stage_start = datetime.now()
            
            job_id = self.create_fine_tuning_job(
                training_samples=training_samples,
                job_name=job_name
            )
            
            results['stages']['job_creation'] = {
                'status': 'completed',
                'duration_seconds': (datetime.now() - stage_start).total_seconds(),
                'job_id': job_id
            }
            
            # Stage 3: Monitoring (optional)
            if wait_for_completion:
                logger.info("Stage 3: Monitoring job completion")
                stage_start = datetime.now()
                
                # Monitor job until completion (with timeout)
                timeout_hours = TRAINING_CONFIG.get('job_timeout_hours', 24)
                timeout_time = datetime.now() + timedelta(hours=timeout_hours)
                
                while datetime.now() < timeout_time:
                    job_status = self.monitor_training_job(job_id)
                    
                    if job_status['status'] in ['completed', 'failed', 'cancelled']:
                        break
                    
                    # Wait before next check
                    import time
                    time.sleep(60)  # Check every minute
                
                results['stages']['monitoring'] = {
                    'status': 'completed',
                    'duration_seconds': (datetime.now() - stage_start).total_seconds(),
                    'final_job_status': job_status['status']
                }
            
            # Pipeline completion
            results['status'] = 'completed'
            results['completed_at'] = datetime.now()
            results['total_duration_seconds'] = (datetime.now() - pipeline_start_time).total_seconds()
            results['job_id'] = job_id
            
            logger.info(f"Fine-tuning pipeline completed successfully: {results['pipeline_id']}")
            return results
            
        except Exception as e:
            logger.error(f"Error in fine-tuning pipeline: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)
            results['completed_at'] = datetime.now()
            results['total_duration_seconds'] = (datetime.now() - pipeline_start_time).total_seconds()
            
            raise
