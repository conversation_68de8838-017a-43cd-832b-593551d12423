"""
Fine-Tuned LLM Indicator Model

This module integrates fine-tuned LLM models with the existing predictor architecture,
providing a consistent interface for market movement prediction using fine-tuned models.
"""

import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

from db.database import get_db_manager
from predictor.fine_tuning.config import (
    FINE_TUNING_CONFIG,
    INFLUENCE_SCORE_MAPPING,
    MODEL_CONFIG
)
from predictor.fine_tuning.gemini_fine_tuner import GeminiFineTuner
from predictor.config import CLASS_LABELS
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


class FineTunedIndicatorModel:
    """
    Fine-tuned LLM model for market movement indication.
    
    Provides a consistent interface with the existing indicator models
    while using fine-tuned LLM capabilities for prediction.
    """
    
    def __init__(self, model_name: Optional[str] = None, job_id: Optional[str] = None):
        self.db = get_db_manager()
        self.fine_tuner = GeminiFineTuner()
        
        # Model identification
        self.model_name = model_name
        self.job_id = job_id
        self.fine_tuned_model_name = None
        
        # Model metadata
        self.model_metadata = {}
        self.is_loaded = False
        
        # Configuration
        self.score_mapping = INFLUENCE_SCORE_MAPPING
        self.class_labels = CLASS_LABELS
        
        # Load model if specified
        if job_id:
            self.load_from_job(job_id)
        elif model_name:
            self.load_model(model_name)
    
    def load_from_job(self, job_id: str) -> bool:
        """
        Load a fine-tuned model from a completed training job.
        
        Args:
            job_id: ID of the completed fine-tuning job
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            # Get job information from database
            job = self.db.fine_tuning_service.get_fine_tuning_job(job_id)
            if not job:
                logger.error(f"Fine-tuning job {job_id} not found")
                return False
            
            if job['status'] != 'completed':
                logger.error(f"Fine-tuning job {job_id} is not completed (status: {job['status']})")
                return False
            
            # Set model information
            self.job_id = job_id
            self.model_name = job.get('fine_tuned_model_name') or f"fine_tuned_{job_id}"
            self.fine_tuned_model_name = job.get('fine_tuned_model_name')
            
            # Load model metadata
            self.model_metadata = {
                'job_id': job_id,
                'base_model': job.get('model_name'),
                'training_config': job.get('training_config', {}),
                'data_config': job.get('data_config', {}),
                'final_metrics': job.get('final_metrics', {}),
                'training_samples_count': job.get('training_samples_count'),
                'created_at': job.get('created_at'),
                'completed_at': job.get('completed_at')
            }
            
            self.is_loaded = True
            logger.info(f"Loaded fine-tuned model from job {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model from job {job_id}: {e}")
            return False
    
    def load_model(self, model_name: str) -> bool:
        """
        Load a fine-tuned model by name.
        
        Args:
            model_name: Name of the fine-tuned model
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        try:
            # Find job with this model name
            jobs = self.db.fine_tuning_service.get_fine_tuning_jobs(
                api='gemini',
                status=['completed']
            )
            
            target_job = None
            for job in jobs:
                if (job.get('fine_tuned_model_name') == model_name or 
                    job.get('model_name') == model_name):
                    target_job = job
                    break
            
            if not target_job:
                logger.error(f"Fine-tuned model {model_name} not found")
                return False
            
            return self.load_from_job(target_job['id'])
            
        except Exception as e:
            logger.error(f"Error loading model {model_name}: {e}")
            return False
    
    def predict_single(self, article_text: str, article_metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Predict influence score for a single article.
        
        Args:
            article_text: Article content to analyze
            article_metadata: Optional metadata (title, source, etc.)
            
        Returns:
            Prediction results with confidence scores
        """
        if not self.is_loaded:
            raise ValueError("Model not loaded. Call load_model() or load_from_job() first.")
        
        try:
            # Prepare article data
            metadata = article_metadata or {}
            title = metadata.get('title', '')
            source = metadata.get('source', '')
            date = metadata.get('date', datetime.now().strftime('%Y-%m-%d'))
            
            # Create prediction prompt based on training format
            output_format = self.model_metadata.get('data_config', {}).get('output_format', 'classification')
            
            if output_format == 'classification':
                system_prompt = """You are a financial news analyst. Analyze the given news article and predict its influence on the S&P 500 index. 
Classify the influence as: very_negative, negative, slightly_negative, neutral, slightly_positive, positive, or very_positive."""
                
                user_prompt = f"""Article Title: {title}

Article Content: {article_text}

Source: {source}
Date: {date}

Analyze this financial news article and classify its potential influence on the S&P 500 index."""
            
            else:  # regression
                system_prompt = """You are a financial news analyst. Analyze the given news article and predict its influence on the S&P 500 index.
Provide a numerical influence score from -5 (very negative) to +5 (very positive), where 0 is neutral."""
                
                user_prompt = f"""Article Title: {title}

Article Content: {article_text}

Source: {source}
Date: {date}

Analyze this financial news article and provide a numerical influence score from -5 to +5."""
            
            # Make prediction using the fine-tuned model
            # Note: This would use the actual fine-tuned model
            # For now, we'll simulate the prediction
            
            prediction_result = self._simulate_prediction(article_text, output_format)
            
            # Format results to match existing indicator interface
            result = {
                'prediction': prediction_result['prediction'],
                'confidence': prediction_result['confidence'],
                'influence_score': prediction_result.get('influence_score'),
                'influence_label': prediction_result.get('influence_label'),
                'model_name': self.model_name,
                'model_type': 'fine_tuned_llm',
                'metadata': {
                    'job_id': self.job_id,
                    'base_model': self.model_metadata.get('base_model'),
                    'output_format': output_format,
                    'article_metadata': metadata
                }
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error making prediction: {e}")
            raise
    
    def predict_batch(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Predict influence scores for multiple articles.
        
        Args:
            articles: List of article dictionaries with 'content' and optional metadata
            
        Returns:
            List of prediction results
        """
        if not self.is_loaded:
            raise ValueError("Model not loaded. Call load_model() or load_from_job() first.")
        
        results = []
        for article in articles:
            try:
                content = article.get('content', '')
                metadata = {k: v for k, v in article.items() if k != 'content'}
                
                prediction = self.predict_single(content, metadata)
                prediction['article_id'] = article.get('id')
                results.append(prediction)
                
            except Exception as e:
                logger.error(f"Error predicting article {article.get('id', 'unknown')}: {e}")
                # Add error result
                results.append({
                    'article_id': article.get('id'),
                    'prediction': 'neutral',
                    'confidence': 0.0,
                    'error': str(e),
                    'model_name': self.model_name,
                    'model_type': 'fine_tuned_llm'
                })
        
        return results
    
    def _simulate_prediction(self, article_text: str, output_format: str) -> Dict[str, Any]:
        """
        Simulate prediction for demonstration purposes.
        In production, this would use the actual fine-tuned model.
        """
        import random
        
        # Simulate prediction based on simple heuristics
        text_lower = article_text.lower()
        
        # Simple sentiment-based scoring
        positive_words = ['growth', 'profit', 'gain', 'rise', 'increase', 'strong', 'positive']
        negative_words = ['loss', 'decline', 'fall', 'decrease', 'weak', 'negative', 'crisis']
        
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        # Calculate influence score
        if positive_count > negative_count:
            influence_score = min(3, positive_count - negative_count)
        elif negative_count > positive_count:
            influence_score = max(-3, positive_count - negative_count)
        else:
            influence_score = 0
        
        # Add some randomness
        influence_score += random.randint(-1, 1)
        influence_score = max(-5, min(5, influence_score))
        
        # Convert to label
        influence_label = self.score_mapping['score_to_label'][influence_score]
        
        # Calculate confidence (simulated)
        confidence = random.uniform(0.6, 0.9)
        
        if output_format == 'classification':
            return {
                'prediction': influence_label,
                'confidence': confidence,
                'influence_score': influence_score,
                'influence_label': influence_label
            }
        else:  # regression
            return {
                'prediction': influence_score,
                'confidence': confidence,
                'influence_score': influence_score,
                'influence_label': influence_label
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the loaded model.
        
        Returns:
            Model information dictionary
        """
        return {
            'model_name': self.model_name,
            'job_id': self.job_id,
            'fine_tuned_model_name': self.fine_tuned_model_name,
            'is_loaded': self.is_loaded,
            'metadata': self.model_metadata
        }
    
    def list_available_models(self) -> List[Dict[str, Any]]:
        """
        List all available fine-tuned models.
        
        Returns:
            List of available model information
        """
        try:
            jobs = self.db.fine_tuning_service.get_fine_tuning_jobs(
                api='gemini',
                status=['completed']
            )
            
            models = []
            for job in jobs:
                model_info = {
                    'job_id': job['id'],
                    'model_name': job.get('fine_tuned_model_name') or f"fine_tuned_{job['id']}",
                    'base_model': job.get('model_name'),
                    'created_at': job.get('created_at'),
                    'completed_at': job.get('completed_at'),
                    'training_samples': job.get('training_samples_count'),
                    'final_metrics': job.get('final_metrics', {})
                }
                models.append(model_info)
            
            return models
            
        except Exception as e:
            logger.error(f"Error listing available models: {e}")
            return []
