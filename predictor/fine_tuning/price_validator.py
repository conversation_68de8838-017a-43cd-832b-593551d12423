"""
Price Validation Filter for Fine-Tuning Data

This module implements price validation logic that filters training samples
based on SPY price movements to ensure influence scores correlate with
actual market movements.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

from db.database import get_db_manager
from predictor.fine_tuning.config import DATA_CONFIG, VALIDATION_CONFIG
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


@dataclass
class PriceValidationResult:
    """Result of price validation for a training sample."""
    is_valid: bool
    correlation_score: float
    direction_match: bool
    price_change_percent: float
    validation_days: int
    reason: str


class PriceMovementCalculator:
    """Calculates price movements for validation."""
    
    def __init__(self, db_manager=None):
        self.db = db_manager or get_db_manager()
        self.ticker = DATA_CONFIG['spy_ticker']
        
    def get_price_data(
        self, 
        start_date: datetime, 
        end_date: datetime
    ) -> pd.DataFrame:
        """
        Get SPY price data for the specified date range.
        
        Args:
            start_date: Start date for price data
            end_date: End date for price data
            
        Returns:
            DataFrame with price data
        """
        try:
            price_data = self.db.market_data_service.get_market_data(
                ticker=self.ticker,
                interval='1d',
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d')
            )
            
            if price_data.empty:
                logger.warning(f"No price data found for {self.ticker} from {start_date} to {end_date}")
                return pd.DataFrame()
            
            # Ensure date column is datetime
            if 'date' in price_data.columns:
                price_data['date'] = pd.to_datetime(price_data['date'])
                price_data.set_index('date', inplace=True)
            
            return price_data
            
        except Exception as e:
            logger.error(f"Error fetching price data: {e}")
            return pd.DataFrame()
    
    def calculate_price_change(
        self, 
        article_date: datetime, 
        validation_days: int
    ) -> Optional[float]:
        """
        Calculate price change percentage from article date to validation_days later.
        
        Args:
            article_date: Date of the article
            validation_days: Number of days to look ahead
            
        Returns:
            Price change percentage or None if data unavailable
        """
        try:
            # Get price data for the period
            end_date = article_date + timedelta(days=validation_days + 5)  # Buffer for weekends
            price_data = self.get_price_data(article_date, end_date)
            
            if price_data.empty:
                return None
            
            # Find the closest trading day to article date
            article_date_normalized = article_date.date()
            available_dates = [d.date() for d in price_data.index]
            
            # Find start price (on or after article date)
            start_date = None
            for date in sorted(available_dates):
                if date >= article_date_normalized:
                    start_date = date
                    break
            
            if start_date is None:
                return None
            
            # Find end price (validation_days trading days later)
            trading_days_count = 0
            end_date = None
            
            for date in sorted(available_dates):
                if date > start_date:
                    trading_days_count += 1
                    if trading_days_count >= validation_days:
                        end_date = date
                        break
            
            if end_date is None:
                return None
            
            # Calculate price change
            start_price = price_data.loc[pd.Timestamp(start_date), 'close_price']
            end_price = price_data.loc[pd.Timestamp(end_date), 'close_price']
            
            price_change_percent = ((end_price - start_price) / start_price) * 100
            
            logger.debug(f"Price change from {start_date} to {end_date}: {price_change_percent:.2f}%")
            return price_change_percent
            
        except Exception as e:
            logger.error(f"Error calculating price change for {article_date}: {e}")
            return None


class PriceValidator:
    """Validates training samples against actual price movements."""
    
    def __init__(self, validation_config: Optional[Dict[str, Any]] = None):
        self.config = validation_config or VALIDATION_CONFIG['price_validation']
        self.calculator = PriceMovementCalculator()
        
    def validate_sample(
        self, 
        training_sample: Dict[str, Any], 
        validation_days: int = None
    ) -> PriceValidationResult:
        """
        Validate a training sample against actual price movements.
        
        Args:
            training_sample: Training sample with metadata
            validation_days: Days to look ahead for validation
            
        Returns:
            PriceValidationResult with validation outcome
        """
        if not self.config['enabled']:
            return PriceValidationResult(
                is_valid=True,
                correlation_score=1.0,
                direction_match=True,
                price_change_percent=0.0,
                validation_days=0,
                reason="Price validation disabled"
            )
        
        validation_days = validation_days or self.config.get('lookback_days', [3])[0]
        
        try:
            metadata = training_sample.get('metadata', {})
            influence_score = metadata.get('influence_score')
            article_date_str = metadata.get('date')
            
            if not influence_score or not article_date_str:
                return PriceValidationResult(
                    is_valid=False,
                    correlation_score=0.0,
                    direction_match=False,
                    price_change_percent=0.0,
                    validation_days=validation_days,
                    reason="Missing influence score or date"
                )
            
            # Parse article date
            if isinstance(article_date_str, str):
                article_date = datetime.strptime(article_date_str, '%Y-%m-%d')
            else:
                article_date = article_date_str
            
            # Calculate actual price change
            price_change_percent = self.calculator.calculate_price_change(
                article_date, validation_days
            )
            
            if price_change_percent is None:
                return PriceValidationResult(
                    is_valid=False,
                    correlation_score=0.0,
                    direction_match=False,
                    price_change_percent=0.0,
                    validation_days=validation_days,
                    reason="Price data unavailable"
                )
            
            # Determine influence direction
            influence_direction = 1 if influence_score > 0 else (-1 if influence_score < 0 else 0)
            price_direction = 1 if price_change_percent > 0 else (-1 if price_change_percent < 0 else 0)
            
            # Check direction match
            direction_match = (influence_direction * price_direction) >= 0
            
            # Calculate correlation score (simplified)
            # Normalize influence score to [-1, 1] range
            normalized_influence = influence_score / 5.0
            normalized_price_change = np.tanh(price_change_percent / 5.0)  # Sigmoid-like normalization
            
            correlation_score = abs(normalized_influence * normalized_price_change)
            
            # Determine if sample is valid
            is_valid = (
                correlation_score >= self.config['correlation_threshold'] and
                direction_match
            )
            
            reason = "Valid" if is_valid else f"Low correlation ({correlation_score:.3f}) or direction mismatch"
            
            return PriceValidationResult(
                is_valid=is_valid,
                correlation_score=correlation_score,
                direction_match=direction_match,
                price_change_percent=price_change_percent,
                validation_days=validation_days,
                reason=reason
            )
            
        except Exception as e:
            logger.error(f"Error validating sample: {e}")
            return PriceValidationResult(
                is_valid=False,
                correlation_score=0.0,
                direction_match=False,
                price_change_percent=0.0,
                validation_days=validation_days,
                reason=f"Validation error: {str(e)}"
            )


class PriceValidationFilter:
    """Filters training datasets based on price validation."""
    
    def __init__(self, validation_config: Optional[Dict[str, Any]] = None):
        self.validator = PriceValidator(validation_config)
        self.config = validation_config or VALIDATION_CONFIG['price_validation']
        
    def filter_training_samples(
        self, 
        training_samples: List[Dict[str, Any]],
        validation_days: int = None
    ) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Filter training samples based on price validation.
        
        Args:
            training_samples: List of training samples
            validation_days: Days to look ahead for validation
            
        Returns:
            Tuple of (filtered_samples, validation_statistics)
        """
        if not self.config['enabled']:
            logger.info("Price validation disabled, returning all samples")
            return training_samples, {'validation_enabled': False}
        
        logger.info(f"Filtering {len(training_samples)} training samples with price validation")
        
        valid_samples = []
        validation_results = []
        
        for sample in training_samples:
            result = self.validator.validate_sample(sample, validation_days)
            validation_results.append(result)
            
            if result.is_valid:
                # Add validation metadata to sample
                sample['metadata']['price_validation'] = {
                    'correlation_score': result.correlation_score,
                    'direction_match': result.direction_match,
                    'price_change_percent': result.price_change_percent,
                    'validation_days': result.validation_days
                }
                valid_samples.append(sample)
        
        # Generate statistics
        stats = self._generate_validation_statistics(validation_results)
        
        logger.info(f"Price validation complete: {len(valid_samples)}/{len(training_samples)} samples passed")
        return valid_samples, stats
    
    def _generate_validation_statistics(
        self, 
        results: List[PriceValidationResult]
    ) -> Dict[str, Any]:
        """Generate statistics about price validation results."""
        total_samples = len(results)
        valid_samples = sum(1 for r in results if r.is_valid)
        
        direction_matches = sum(1 for r in results if r.direction_match)
        correlation_scores = [r.correlation_score for r in results if r.correlation_score > 0]
        price_changes = [r.price_change_percent for r in results if r.price_change_percent != 0]
        
        stats = {
            'validation_enabled': True,
            'total_samples': total_samples,
            'valid_samples': valid_samples,
            'validation_rate': valid_samples / total_samples if total_samples > 0 else 0,
            'direction_match_rate': direction_matches / total_samples if total_samples > 0 else 0,
            'average_correlation': np.mean(correlation_scores) if correlation_scores else 0,
            'average_price_change': np.mean(price_changes) if price_changes else 0,
            'price_change_std': np.std(price_changes) if price_changes else 0,
            'validation_reasons': {}
        }
        
        # Count validation failure reasons
        for result in results:
            if not result.is_valid:
                reason = result.reason
                stats['validation_reasons'][reason] = stats['validation_reasons'].get(reason, 0) + 1
        
        return stats
