"""
Data formatter for LLM-based market prediction.

This module formats news articles with sentiment scores and technical indicators
into prompts suitable for LLM market prediction.
"""

import json
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import pytz

from nlp.utils import truncate_text
from predictor.config import MAX_WORDS_PER_ARTICLE
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)

TZ_NAME = "America/New_York"


class LLMDataFormatter:
    """Formats news and technical data for LLM market prediction prompts."""

    def __init__(self):
        """Initialize the data formatter."""
        self.article_metadata = {}  # Store article metadata for reference mapping

    def format_articles_for_prompt(
        self,
        articles: List[Dict[str, Any]],
        max_articles: int = 20,
        include_sentiment: bool = False
    ) -> str:
        """
        Format news articles for inclusion in LLM prompt.

        Args:
            articles: List of article dictionaries with content and metadata
            max_articles: Maximum number of articles to include
            include_sentiment: Whether to include sentiment scores

        Returns:
            Formatted string of articles for prompt
        """
        if not articles:
            return "No recent news articles available."

        # Sort articles by publish time (most recent first)
        sorted_articles = sorted(
            articles,
            key=lambda x: x.get('date', ''),
            reverse=True
        )[:max_articles]

        formatted_articles = []

        # Clear previous metadata
        self.article_metadata = {}

        for i, article in enumerate(sorted_articles, 1):
            # Extract basic info
            title = article.get('title', 'No title')
            source = article.get('source', 'Unknown source')
            publish_time = article.get('date', '')
            content = article.get('content', '')
            url = article.get('url', '')

            # Store article metadata for later reference
            article_ref = f"Article {i}"
            self.article_metadata[article_ref] = {
                'title': title,
                'source': source,
                'url': url,
                'publish_time': publish_time
            }

            # Format publish time
            time_str = self._format_article_time(publish_time)

            # Truncate content if too long
            truncated_content = truncate_text(content, MAX_WORDS_PER_ARTICLE, respect_sentences=True)
            content_preview = truncated_content + "..." if len(
                truncated_content) < len(content) else truncated_content

            # Build article text
            article_text = f"Article {i}:\n"
            article_text += f"Source: {source}\n"
            article_text += f"Time: {time_str}\n"
            article_text += f"Title: {title}\n"
            article_text += f"Content: {content_preview}\n"

            # Add sentiment if available and requested
            if include_sentiment and 'sentiment_score' in article:
                sentiment_score = article['sentiment_score']
                sentiment_label = self._get_sentiment_label(sentiment_score)
                article_text += f"Sentiment: {sentiment_label} (score: {sentiment_score:.3f})\n"

            formatted_articles.append(article_text)

        return "\n".join(formatted_articles)

    def get_article_metadata(self) -> Dict[str, Dict[str, str]]:
        """Get stored article metadata for reference mapping."""
        return self.article_metadata

    def format_technical_indicators(
        self,
        current_price: float,
        price_data: Optional[pd.DataFrame] = None,
        vix_level: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        Format technical indicators for LLM prompt.

        Args:
            current_price: Current index fund price
            price_data: Optional DataFrame with OHLCV data
            vix_level: Optional VIX level

        Returns:
            Dictionary of formatted technical indicators
        """
        # Handle None or empty price data
        if price_data is None or price_data.empty:
            logger.warning("No price data provided, using basic indicators")
            return {
                'index_level': current_price,
                'index_change_pct': 0.0,
                'index_change_pct_week': 0.0,
                'vix_level': vix_level,
                'volume_vs_avg': 1.0
            }

        # Calculate price change
        if len(price_data) >= 2:
            prev_close = price_data['Close'].iloc[-2]
            price_change_pct = (
                (current_price - prev_close) / prev_close) * 100
        else:
            price_change_pct = 0.0

        if len(price_data) >= 8:
            prev_close_week = price_data['Close'].iloc[-8]
            price_change_pct_week = (
                (current_price - prev_close_week) / prev_close_week) * 100
        else:
            price_change_pct_week = 0.0

        # Calculate volume vs average
        volume_vs_avg = 1.0
        if 'Volume' in price_data.columns and len(price_data) >= 20:
            recent_volume = price_data['Volume'].iloc[-1]
            avg_volume = price_data['Volume'].tail(20).mean()
            if avg_volume > 0:
                volume_vs_avg = recent_volume / avg_volume

        return {
            'index_level': current_price,
            'index_change_pct': price_change_pct,
            'index_change_pct_week': price_change_pct_week,
            'vix_level': vix_level,
            'volume_vs_avg': volume_vs_avg
        }

    def _format_article_time(self, publish_time: str) -> str:
        """Format article publish time for display."""
        if not publish_time:
            return "Unknown time"

        try:
            # Try to parse ISO format
            if 'T' in publish_time:
                dt = datetime.fromisoformat(
                    publish_time.replace('Z', '+00:00'))
            else:
                dt = datetime.strptime(publish_time, '%Y-%m-%d')

            return dt.strftime('%Y-%m-%d %H:%M')
        except (ValueError, TypeError):
            return str(publish_time)

    def _get_sentiment_label(self, sentiment_score: float) -> str:
        """Convert sentiment score to label."""
        if sentiment_score > 0.1:
            return "Positive"
        elif sentiment_score < -0.1:
            return "Negative"
        else:
            return "Neutral"

    def create_market_prediction_prompt(
        self,
        articles: List[Dict[str, Any]],
        technical_indicators: Dict[str, Any],
        max_articles: int = 30
    ) -> Tuple[str, str]:
        """
        Create the complete market prediction prompt.

        Args:
            articles: List of news articles with sentiment
            technical_indicators: Technical indicator data
            max_articles: Maximum articles to include

        Returns:
            Tuple of (user_prompt, system_prompt)
        """
        from nlp.llm.prompt import PromptManager

        # Get the prompt templates
        prompt_data = PromptManager.get_prompt("market_prediction")
        system_prompt = prompt_data["system_prompt"]
        prompt_template = prompt_data["prompt_template"]

        # Format articles
        articles_text = self.format_articles_for_prompt(
            articles,
            max_articles=max_articles
        )

        # Create user prompt
        user_prompt = prompt_template.format(
            index_level=technical_indicators['index_level'],
            index_change_pct=technical_indicators['index_change_pct'],
            index_change_pct_week=technical_indicators['index_change_pct_week'],
            vix_level=technical_indicators['vix_level'],
            volume_vs_avg=technical_indicators['volume_vs_avg'],
            articles_text=articles_text,
            num_articles=min(len(articles), max_articles),
            time=datetime.now(
                pytz.timezone(TZ_NAME)).strftime('%Y-%m-%d %H:%M EST')
        )

        return user_prompt, system_prompt
