#!/usr/bin/env python3
"""
Test script for the LLM predictor module.

This script tests the LLM predictor functionality with sample data.
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from predictor.llm.llm_predictor import LLMPredictor
from predictor.llm.data_formatter import LLMDataFormatter
from predictor.llm.response_parser import LLMResponseParser
from utils.logging_config import get_predictor_logger

# Configure logger
logger = get_predictor_logger(__name__)


def create_sample_articles() -> List[Dict[str, Any]]:
    """Create sample news articles for testing."""
    return [
        {
            'title': 'Fed Signals Potential Rate Cut as Inflation Cools',
            'content': 'The Federal Reserve indicated today that it may consider cutting interest rates in the coming months as inflation continues to moderate. Recent economic data shows consumer prices rising at the slowest pace in two years.',
            'source': 'Reuters',
            'publish_time': '2024-01-15T14:30:00Z',
            'sentiment_score': 0.3,
            'url': 'https://example.com/fed-rate-cut'
        },
        {
            'title': 'Tech Stocks Rally on Strong Earnings Reports',
            'content': 'Major technology companies reported better-than-expected quarterly earnings, driving a broad rally in tech stocks. Apple, Microsoft, and Google all beat analyst estimates.',
            'source': 'CNBC',
            'publish_time': '2024-01-15T16:45:00Z',
            'sentiment_score': 0.6,
            'url': 'https://example.com/tech-rally'
        },
        {
            'title': 'Geopolitical Tensions Rise as Trade Disputes Escalate',
            'content': 'International trade tensions have escalated following new tariff announcements, raising concerns about global economic growth and supply chain disruptions.',
            'source': 'Bloomberg',
            'publish_time': '2024-01-15T12:15:00Z',
            'sentiment_score': -0.4,
            'url': 'https://example.com/trade-tensions'
        }
    ]


async def test_data_formatter():
    """Test the data formatter functionality."""
    logger.info("Testing LLM Data Formatter...")
    
    formatter = LLMDataFormatter()
    articles = create_sample_articles()
    
    # Test article formatting
    formatted_articles = formatter.format_articles_for_prompt(articles, max_articles=5)
    logger.info(f"Formatted articles length: {len(formatted_articles)}")
    
    # Test technical indicators formatting
    technical_indicators = formatter.format_technical_indicators(
        current_price=450.25,
        price_data=None,  # Empty DataFrame for testing
        vix_level=18.5
    )
    logger.info(f"Technical indicators: {technical_indicators}")
    
    # Test prompt creation
    user_prompt, system_prompt = formatter.create_market_prediction_prompt(
        articles=articles,
        technical_indicators=technical_indicators,
        max_articles=3
    )
    
    logger.info(f"System prompt length: {len(system_prompt)}")
    logger.info(f"User prompt length: {len(user_prompt)}")
    
    return True


async def test_response_parser():
    """Test the response parser functionality."""
    logger.info("Testing LLM Response Parser...")
    
    parser = LLMResponseParser()
    
    # Test with sample JSON response
    sample_json_response = '''
    {
        "short_term": {
            "direction": "UP",
            "expected_move_pct": "2-4%",
            "confidence": "HIGH",
            "key_evidence": ["Fed rate cut signals", "Strong tech earnings", "Positive market sentiment"]
        },
        "medium_term": {
            "direction": "UP",
            "expected_move_pct": "5-8%",
            "confidence": "MEDIUM",
            "key_evidence": ["Economic recovery continues", "Corporate earnings growth"]
        },
        "long_term": {
            "direction": "FLAT",
            "expected_move_pct": "0-3%",
            "confidence": "LOW",
            "key_evidence": ["Geopolitical uncertainties", "Inflation concerns"]
        },
        "dominant_theme": "Federal Reserve policy shift driving market optimism",
        "critical_levels": {
            "support": 440.0,
            "resistance": 465.0,
            "trigger": "Break above 465 for continued uptrend"
        }
    }
    '''
    
    parsed_result = parser.parse_market_prediction(
        response_content=sample_json_response,
        api_name="test_api",
        model_name="test_model"
    )
    
    logger.info(f"Parsed prediction: {parsed_result['prediction']}")
    logger.info(f"Confidence: {parsed_result['confidence']}")
    logger.info(f"Probabilities: {parsed_result['probabilities']}")
    
    return True


async def test_llm_predictor_mock():
    """Test the LLM predictor with mock data (no actual API calls)."""
    logger.info("Testing LLM Predictor (mock mode)...")
    
    # Create predictor without API configs (will fail gracefully)
    predictor = LLMPredictor(api_configs={})
    
    articles = create_sample_articles()
    current_price = 450.25
    
    # This should return a fallback prediction since no APIs are configured
    result = await predictor.predict_market_direction(
        articles=articles,
        current_price=current_price,
        preferred_api="openai"
    )
    
    logger.info(f"Mock prediction result: {result['prediction']}")
    logger.info(f"Mock confidence: {result['confidence']}")
    logger.info(f"Mock metadata: {result['metadata']}")
    
    return True


async def test_llm_predictor_with_api():
    """Test the LLM predictor with actual API (if configured)."""
    logger.info("Testing LLM Predictor with API...")
    
    try:
        # Try to initialize with default configs
        predictor = LLMPredictor()
        
        # Check if any APIs are available
        supported_apis = predictor.get_supported_apis()
        logger.info(f"Supported APIs: {supported_apis}")
        
        if not supported_apis:
            logger.warning("No APIs configured, skipping API test")
            return True
        
        # Test API connections
        for api_name in supported_apis:
            is_connected = await predictor.test_api_connection(api_name)
            logger.info(f"API {api_name} connection test: {'PASS' if is_connected else 'FAIL'}")
        
        # If we have working APIs, try a real prediction
        articles = create_sample_articles()
        current_price = 450.25
        
        result = await predictor.predict_market_direction(
            articles=articles,
            current_price=current_price,
            preferred_api=supported_apis[0] if supported_apis else "openai"
        )
        
        logger.info(f"API prediction result: {result['prediction']}")
        logger.info(f"API confidence: {result['confidence']}")
        logger.info(f"API used: {result['metadata'].get('api', 'unknown')}")
        
        return True
        
    except Exception as e:
        logger.error(f"API test failed: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("Starting LLM Predictor Tests...")
    
    tests = [
        ("Data Formatter", test_data_formatter),
        ("Response Parser", test_response_parser),
        ("LLM Predictor (Mock)", test_llm_predictor_mock),
        ("LLM Predictor (API)", test_llm_predictor_with_api)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            result = await test_func()
            results[test_name] = "PASS" if result else "FAIL"
            
        except Exception as e:
            logger.error(f"Test {test_name} failed with error: {e}")
            results[test_name] = "FAIL"
    
    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    for test_name, result in results.items():
        status_symbol = "✓" if result == "PASS" else "✗"
        logger.info(f"{status_symbol} {test_name}: {result}")
    
    total_tests = len(results)
    passed_tests = sum(1 for r in results.values() if r == "PASS")
    
    logger.info(f"\nPassed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        logger.info("All tests passed! 🎉")
    else:
        logger.warning(f"{total_tests - passed_tests} test(s) failed")


if __name__ == "__main__":
    asyncio.run(main())
