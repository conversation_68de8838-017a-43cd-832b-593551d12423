#!/usr/bin/env python3
"""
Shared data preparation utilities for LLM predictions.

This module provides unified data preparation functions that can be used
by both CLI and web server components to ensure consistency.
"""

import pandas as pd
import pytz
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from nlp.utils import truncate_text
from predictor.config import MAX_ARTICLES_TO_ANALYZE
from utils.logging_config import get_predictor_logger

logger = get_predictor_logger(__name__)

TZ_NAME = "America/New_York"


def get_recent_articles_from_db(
        limit: int = MAX_ARTICLES_TO_ANALYZE,
        days_back: int = 3,
        categories: List[str] = ["Macro News", "Breaking News"]
) -> List[Dict[str, Any]]:
    """
    Get recent articles from the database in the format expected by LLM predictor.

    Args:
        limit: Maximum number of articles to return

    Returns:
        List of formatted articles
    """
    try:
        from db.database import get_db_manager

        db = get_db_manager()
        articles = db.article_service.get_articles_by_influence(
            limit=limit,
            start_date=datetime.now(
                pytz.timezone(TZ_NAME)) - timedelta(days=days_back),
            meta_data_field="influence_tagging",
            category=categories
        )

        # Convert to the format expected by LLM predictor
        formatted_articles = []
        for article in articles:
            formatted_article = {
                'title': article.get('title', ''),
                'content': article.get('content', ''),
                'source': article.get('source', ''),
                'publish_time': article.get('date', ''),
                'url': article.get('url', '')
            }
            formatted_articles.append(formatted_article)

        logger.info(
            f"Retrieved {len(formatted_articles)} articles from database")
        return formatted_articles

    except Exception as e:
        logger.error(f"Error retrieving articles from database: {e}")
        return []


def get_index_price_data() -> Optional[pd.DataFrame]:
    """
    Get current index price data using Yahoo Finance API.

    Returns:
        DataFrame with price data or None if failed
    """
    try:
        from apis.yahoo_finance import YahooFinanceAPI

        yahoo_api = YahooFinanceAPI()
        df = yahoo_api.get_price_data(
            ticker="SPY",
            interval="1d",
            period="30d"
        )
        return df
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return None


def get_vix_level() -> Optional[float]:
    """
    Get current VIX level.

    Returns:
        Current VIX level or None if failed
    """
    try:
        import yfinance as yf
        vix = yf.Ticker("^VIX")
        vix_data = vix.history(period="5d")
        return float(vix_data['Close'].iloc[-1])
    except Exception as e:
        logger.error(f"Error fetching VIX data: {e}")
        return None


def prepare_prediction_data(
        days_back: int = 7,
        max_articles: int = MAX_ARTICLES_TO_ANALYZE,
        categories: List[str] = ["Macro News", "Breaking News"]
) -> Dict[str, Any]:
    """
    Prepare all data needed for LLM prediction.

    Args:
        max_articles: Maximum number of articles to retrieve

    Returns:
        Dictionary containing all prepared data
    """
    logger.info(f"Preparing prediction data with {max_articles} articles")

    # Get articles
    articles = get_recent_articles_from_db(limit=max_articles, days_back=days_back, categories=categories)

    price_data = get_index_price_data()

    # Get current price
    current_price = 500.0  # Default fallback
    if price_data is not None and not price_data.empty:
        current_price = float(price_data['Close'].iloc[-1])

    # Get VIX level
    vix_level = get_vix_level() or 20.0

    return {
        'articles': articles,
        'current_price': current_price,
        'price_data': price_data,
        'vix_level': vix_level,
        'num_articles': len(articles)
    }
