# LLM-based SP500 Market Predictor

This module provides Large Language Model (LLM) based prediction capabilities for SP500 market direction using news sentiment and technical indicators.

## Overview

The LLM predictor complements the existing non-transformer models (logistic regression, gradient boosting, LSTM) by providing an LLM-based prediction approach that can reason about news content in natural language.

## Features

- **Multi-API Support**: Works with OpenAI, Anthropic, and Google Gemini APIs
- **News Integration**: Analyzes news articles with sentiment scores
- **Technical Indicators**: Incorporates SP500 price data, VIX levels, and volume data
- **Structured Output**: Provides predictions with confidence scores and reasoning
- **Caching**: Database caching for improved performance
- **Async Processing**: Follows project's async patterns
- **Error Handling**: Robust error handling with fallback predictions

## Components

### Core Modules

1. **`llm_predictor.py`** - Main LLM predictor implementation
2. **`data_formatter.py`** - Formats news and technical data for LLM prompts
3. **`response_parser.py`** - Parses LLM responses into structured predictions

### Model Integration

4. **`../models/llm_model.py`** - LLM model that follows the base model interface
5. **Model Factory Integration** - Added to `../models/model_factory.py`

### Testing and CLI

6. **`test_llm_predictor.py`** - Comprehensive test suite
7. **`predict_cli.py`** - Command-line interface for predictions

## Usage

### Basic Usage

```python
from predictor.llm.llm_predictor import LLMPredictor

# Initialize predictor
predictor = LLMPredictor()

# Make prediction
result = await predictor.predict_market_direction(
    articles=news_articles,
    current_price=450.25,
    preferred_api="openai"
)

print(f"Prediction: {result['prediction']}")
print(f"Confidence: {result['confidence']}")
```

### Using the Model Interface

```python
from predictor.models.model_factory import create_model

# Create LLM model
config = {
    'model_type': 'llm',
    'preferred_api': 'openai',
    'max_articles': 15
}
model = create_model(config)

# Make prediction
result = model.predict_single(
    articles=articles,
    current_price=450.25
)
```

### Command Line Interface

```bash
# Basic prediction
python predictor/llm/predict_cli.py --api openai

# Save results to file
python predictor/llm/predict_cli.py --api anthropic --output prediction.json

# Use specific number of articles
python predictor/llm/predict_cli.py --max-articles 10 --api gemini
```

### Web API Integration

The LLM predictor is integrated into the web interface:

```bash
# Get LLM prediction via web API
curl http://localhost:5000/api/llm-prediction?api=openai
```

## Configuration

### API Configuration

Configure LLM APIs through environment variables:

```bash
# OpenAI
export OPENAI_API_KEY="your-openai-key"

# Anthropic
export ANTHROPIC_API_KEY="your-anthropic-key"

# Google Gemini
export GEMINI_API_KEY="your-gemini-key"
# OR for Vertex AI
export GOOGLE_CLOUD_PROJECT="your-project"
export GOOGLE_CLOUD_LOCATION="us-central1"
```

### Model Configuration

```python
api_configs = {
    'openai': {
        'total_budget': 10.0,
        'requests_per_minute': 30
    },
    'anthropic': {
        'total_budget': 10.0,
        'requests_per_minute': 30
    }
}

predictor = LLMPredictor(
    api_configs=api_configs,
    force_update=False,
    cache_duration_hours=1
)
```

## Output Format

The LLM predictor returns structured predictions:

```json
{
    "prediction": "up",
    "confidence": 0.75,
    "probabilities": {
        "positive": 0.75,
        "negative": 0.15,
        "neutral": 0.10
    },
    "key_evidence": [
        "Fed signals potential rate cuts",
        "Strong corporate earnings",
        "Positive market sentiment"
    ],
    "detailed_outlook": {
        "short_term": {
            "direction": "UP",
            "expected_move_pct": "2-4%",
            "confidence": "HIGH",
            "key_evidence": ["..."]
        },
        "medium_term": {...},
        "long_term": {...}
    },
    "dominant_theme": "Federal Reserve policy shift driving optimism",
    "critical_levels": {
        "support": 440.0,
        "resistance": 465.0,
        "trigger": "Break above 465 for continued uptrend"
    },
    "metadata": {
        "api": "openai",
        "model": "gpt-4",
        "timestamp": "2024-01-15T10:30:00Z"
    }
}
```

## Testing

Run the test suite:

```bash
python predictor/llm/test_llm_predictor.py
```

Tests include:
- Data formatter functionality
- Response parser with various formats
- Mock predictions (no API calls)
- Real API connections (if configured)

## Integration with Existing System

The LLM predictor integrates seamlessly with the existing NewsMonitor system:

1. **Database Integration**: Uses existing `DatabaseManager` for caching
2. **Logging**: Follows project logging conventions
3. **Model Interface**: Implements `BasePredictionModel` interface
4. **Web Interface**: Provides API endpoints for web consumption
5. **Async Patterns**: Uses async/await for non-blocking operations

## Error Handling

The system provides robust error handling:

- **API Failures**: Automatic fallback to other configured APIs
- **Parsing Errors**: Graceful degradation with fallback predictions
- **Network Issues**: Retry logic with exponential backoff
- **Missing Data**: Default values and sample data fallbacks

## Performance Considerations

- **Caching**: Database caching reduces API calls
- **Rate Limiting**: Respects API rate limits
- **Async Processing**: Non-blocking operations
- **Token Management**: Efficient prompt construction

## Future Enhancements

Potential improvements:
- **Ensemble Predictions**: Combine multiple LLM predictions
- **Fine-tuning**: Custom model fine-tuning on financial data
- **Real-time Updates**: Streaming predictions with live data
- **Advanced Caching**: More sophisticated cache invalidation
- **Model Comparison**: A/B testing between different LLMs
