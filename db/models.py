from typing import Any, Dict
from sqlalchemy import <PERSON><PERSON>Y, JSON, BigInteger, Boolean, Column, Date, Float, String, Text, DateTime, ForeignKey, Integer, Index
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector
from sqlalchemy.orm import declarative_base
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import expression

Base = declarative_base()


class Article(Base):
    __tablename__ = "articles"

    id = Column(String, primary_key=True)
    title = Column(String, nullable=False)
    content = Column(Text)
    url = Column(String, nullable=False, unique=True)
    image_url = Column(String)
    author = Column(String)
    source = Column(String)
    publish_time = Column(DateTime(timezone=True))
    update_time = Column(DateTime(timezone=True))
    # update time or publish time
    date = Column(DateTime(timezone=True), nullable=False)
    tags = Column(ARRAY(String))
    crawl_time = Column(DateTime(timezone=True))
    article_metadata = Column(JSONB)  # flexible metadata for filtering

    # Relationships
    chunks = relationship("Chunk", back_populates="article",
                          cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index("ix_articles_date", "date"),
        Index("ix_articles_source", "source"),
        Index("ix_articles_metadata_gin",
              "article_metadata", postgresql_using="gin"),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<Article(title={self.title[:30]!r}, date={self.date})>"

    def to_dict(self, include_chunks: bool = False) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "url": self.url,
            "image_url": self.image_url,
            "author": self.author,
            "source": self.source,
            "publish_time": self.publish_time,
            "update_time": self.update_time,
            "date": self.date,
            "tags": self.tags,
            "crawl_time": self.crawl_time,
            "article_metadata": self.article_metadata,
        }

        if include_chunks and hasattr(self, "chunks"):
            data["chunks"] = [
                {
                    "id": chunk.id,
                    "text": chunk.text,
                    "embedding": chunk.embedding,
                    "chunk_index": chunk.chunk_index,
                    "model_name": chunk.model_name,
                    "chunk_metadata": chunk.chunk_metadata,
                    "article_id": chunk.article_id,
                }
                for chunk in self.chunks
            ]

        return data


class Chunk(Base):
    __tablename__ = "chunks"

    id = Column(String, primary_key=True)

    # source article info
    article_id = Column(String, ForeignKey(
        "articles.id", ondelete="CASCADE"), nullable=False)
    article_date = Column(DateTime(timezone=True), nullable=False)
    article_title = Column(String, nullable=False)
    article_url = Column(String, nullable=False)

    type = Column(String)
    chunk_index = Column(Integer, nullable=False)
    text = Column(Text, nullable=False)
    embedding = Column(Vector(768))
    model_name = Column(String)

    # per-chunk metadata (e.g., topic, sentiment)
    chunk_metadata = Column(JSONB)

    # Relationships
    article = relationship("Article", back_populates="chunks")

    # Indexes for performance
    __table_args__ = (
        Index("ix_chunks_article_url", "article_url"),
        Index("ix_chunks_article_date", "article_date"),
        Index("ix_chunks_article_title", "article_title"),
        Index(
            "ix_chunks_recent_date",
            "article_date",
            postgresql_where=Column(
                "article_date", DateTime) >= expression.literal("2024-01-01")
        ),
        Index("ix_chunks_date_title", "article_date", "article_title"),
        Index("ix_chunks_metadata_gin", "chunk_metadata", postgresql_using="gin"),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<Chunk(article_url={self.article_url}, index={self.chunk_index})>"

    def to_dict(self) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "type": self.type,
            "text": self.text,
            "article_id": self.article_id,
            "article_date": self.article_date,
            "article_title": self.article_title,
            "article_url": self.article_url,
            "embedding": self.embedding,
            "chunk_index": self.chunk_index,
            "model_name": self.model_name,
            "chunk_metadata": self.chunk_metadata,
        }

        return data


class MarketData(Base):
    """Market data model for storing historical price data."""
    __tablename__ = 'market_data'

    ticker = Column(String, primary_key=True)
    date = Column(Date, primary_key=True)
    interval = Column(String, primary_key=True)

    open_price = Column(Float)
    high_price = Column(Float)
    low_price = Column(Float)
    close_price = Column(Float)
    volume = Column(BigInteger)

    # Indexes for performance
    __table_args__ = (
        Index('ix_market_data_open_price', 'open_price'),
        {'extend_existing': True},
    )


class LlmApiResults(Base):
    """Results from LLM APIs."""
    __tablename__ = 'llm_api_results'

    id = Column(String, primary_key=True)
    article_id = Column(String)
    api = Column(String)
    model = Column(String)
    prompt_type = Column(String)
    prompt = Column(Text)
    batch_id = Column(String)
    status = Column(String)
    content = Column(JSONB)
    raw_response = Column(Text)
    cost = Column(Float)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)

    __table_args__ = (
        Index('ix_llm_result_status', 'status'),
        Index('ix_llm_result_api', 'api'),
        Index('ix_llm_result_article_id', 'article_id'),
        Index('ix_llm_result_model', 'model'),
        Index('ix_llm_result_prompt_type', 'prompt_type'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "article_id": self.article_id,
            "api": self.api,
            "model": self.model,
            "prompt_type": self.prompt_type,
            "prompt": self.prompt,
            "batch_id": self.batch_id,
            "status": self.status,
            "content": self.content,
            "raw_response": self.raw_response,
            "cost": self.cost,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens
        }


class LlmFineTuningJobs(Base):
    """LLM fine-tuning jobs tracking model."""
    __tablename__ = 'llm_fine_tuning_jobs'

    id = Column(String, primary_key=True)
    api = Column(String, nullable=False)  # 'gemini', 'openai', etc.
    model_name = Column(String, nullable=False)  # Base model name
    fine_tuned_model_name = Column(String)  # Name of the fine-tuned model
    job_type = Column(String, default='fine_tuning')  # Type of training job
    status = Column(String, nullable=False)  # 'pending', 'running', 'completed', 'failed'

    # Training configuration
    training_config = Column(JSONB)  # Training parameters, hyperparameters
    data_config = Column(JSONB)  # Data filtering and preparation config

    # Job metadata
    created_at = Column(DateTime(timezone=True), nullable=False)
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))

    # Training data info
    training_samples_count = Column(Integer)
    validation_samples_count = Column(Integer)
    training_data_uri = Column(String)  # GCS URI for training data
    validation_data_uri = Column(String)  # GCS URI for validation data

    # Results and metrics
    final_metrics = Column(JSONB)  # Final training metrics
    validation_metrics = Column(JSONB)  # Validation metrics

    # Cost and resource tracking
    cost = Column(Float)
    training_tokens = Column(Integer)

    # Error handling
    error_message = Column(Text)
    raw_response = Column(JSONB)  # Raw API response

    # Indexes
    __table_args__ = (
        Index('ix_fine_tuning_status', 'status'),
        Index('ix_fine_tuning_api', 'api'),
        Index('ix_fine_tuning_created_at', 'created_at'),
        Index('ix_fine_tuning_model_name', 'model_name'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'api': self.api,
            'model_name': self.model_name,
            'fine_tuned_model_name': self.fine_tuned_model_name,
            'job_type': self.job_type,
            'status': self.status,
            'training_config': self.training_config,
            'data_config': self.data_config,
            'created_at': self.created_at,
            'started_at': self.started_at,
            'completed_at': self.completed_at,
            'training_samples_count': self.training_samples_count,
            'validation_samples_count': self.validation_samples_count,
            'training_data_uri': self.training_data_uri,
            'validation_data_uri': self.validation_data_uri,
            'final_metrics': self.final_metrics,
            'validation_metrics': self.validation_metrics,
            'cost': self.cost,
            'training_tokens': self.training_tokens,
            'error_message': self.error_message,
            'raw_response': self.raw_response
        }


class LlmApiBatches(Base):
    """Llm api batches tracking model."""
    __tablename__ = 'llm_api_batches'

    id = Column(String, primary_key=True)
    api = Column(String)
    prompt_type = Column(String)
    status = Column(String)
    created_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))
    raw_response = Column(Text)
    cost = Column(Float)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)

    # Indexes
    __table_args__ = (
        Index('ix_llm_batch_status', 'status'),
        Index('ix_llm_batch_api', 'api'),
        Index('ix_llm_batch_prompt_type', 'prompt_type'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'api': self.api,
            'status': self.status,
            'created_at': self.created_at,
            'completed_at': self.completed_at,
            'expires_at': self.expires_at,
            'prompt_type': self.prompt_type,
            'raw_response': self.raw_response,
            'cost': self.cost,
            'input_tokens': self.input_tokens,
            'output_tokens': self.output_tokens
        }


class LlmPredictions(Base):
    """LLM prediction storage model for market predictions."""
    __tablename__ = 'llm_predictions'

    id = Column(String, primary_key=True)
    api = Column(String, nullable=False)
    model = Column(String)
    prediction_data = Column(JSONB, nullable=False)
    input_metadata = Column(JSONB)

    # Timing
    created_at = Column(DateTime(timezone=True), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Cost tracking
    cost = Column(Float)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)

    # Indexes
    __table_args__ = (
        Index('ix_llm_predictions_api', 'api'),
        Index('ix_llm_predictions_created_at', 'created_at'),
        Index('ix_llm_predictions_expires_at', 'expires_at'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'api': self.api,
            'model': self.model,
            'prediction_data': self.prediction_data,
            'input_metadata': self.input_metadata,
            'created_at': self.created_at,
            'expires_at': self.expires_at,
            'cost': self.cost,
            'input_tokens': self.input_tokens,
            'output_tokens': self.output_tokens
        }
